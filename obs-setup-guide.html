<!DOCTYPE html>
<html lang="de">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>OBS Browser Sources Setup - Squirtvana</title>
    <style>
      body {
        background: linear-gradient(135deg, #1e1b4b 0%, #7c3aed 100%);
        color: white;
        font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
        margin: 0;
        padding: 20px;
        line-height: 1.6;
      }
      .container {
        max-width: 1200px;
        margin: 0 auto;
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 20px;
        padding: 30px;
      }
      h1 {
        color: #fbbf24;
        text-align: center;
        margin-bottom: 30px;
      }
      h2 {
        color: #34d399;
        border-bottom: 2px solid #34d399;
        padding-bottom: 10px;
      }
      h3 {
        color: #60a5fa;
      }

      .step {
        background: rgba(0, 0, 0, 0.3);
        border-left: 4px solid #34d399;
        padding: 20px;
        margin: 20px 0;
        border-radius: 10px;
      }

      .url-box {
        background: rgba(0, 0, 0, 0.5);
        border: 1px solid #60a5fa;
        border-radius: 8px;
        padding: 15px;
        margin: 10px 0;
        font-family: "Courier New", monospace;
        font-size: 14px;
        word-break: break-all;
      }

      .setting {
        background: rgba(139, 92, 246, 0.2);
        border-radius: 8px;
        padding: 10px;
        margin: 8px 0;
      }

      .preview-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 20px;
        margin: 20px 0;
      }

      .preview-card {
        background: rgba(0, 0, 0, 0.3);
        border-radius: 15px;
        padding: 20px;
        text-align: center;
        border: 2px solid transparent;
        transition: all 0.3s;
      }
      .preview-card:hover {
        border-color: #fbbf24;
        transform: translateY(-5px);
      }

      .preview-image {
        width: 100%;
        height: 180px;
        background: linear-gradient(45deg, #667eea, #764ba2);
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        margin-bottom: 15px;
      }

      .quick-copy {
        background: #1f2937;
        border: 1px solid #60a5fa;
        color: white;
        padding: 8px 15px;
        border-radius: 5px;
        cursor: pointer;
        font-size: 12px;
        margin-left: 10px;
      }
      .quick-copy:hover {
        background: #374151;
      }

      .status-indicator {
        display: inline-block;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        margin-right: 8px;
      }
      .status-good {
        background: #10b981;
      }
      .status-warning {
        background: #f59e0b;
      }
      .status-error {
        background: #ef4444;
      }

      .tip {
        background: rgba(251, 191, 36, 0.1);
        border: 1px solid #fbbf24;
        border-radius: 8px;
        padding: 15px;
        margin: 15px 0;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1>🎥 OBS Browser Sources Setup Guide</h1>

      <div class="step">
        <h2>📋 Übersicht - Verfügbare Sources</h2>
        <div class="preview-grid">
          <div class="preview-card">
            <div class="preview-image">🎮 VOLLBILD</div>
            <h3>Squirtvana Hauptapp</h3>
            <div class="url-box">http://localhost:5175</div>
            <p>Komplette App mit allen Funktionen</p>
          </div>

          <div class="preview-card">
            <div class="preview-image">📊 STATUS</div>
            <h3>Nur Status Overlay</h3>
            <div class="url-box">http://localhost:5175?view=status</div>
            <p>Minimales Overlay für Status-Anzeige</p>
          </div>

          <div class="preview-card">
            <div class="preview-image">🎛️ CONTROLS</div>
            <h3>Nur Steuerung</h3>
            <div class="url-box">http://localhost:5175?view=controls</div>
            <p>Control Panel für Interaktion</p>
          </div>

          <div class="preview-card">
            <div class="preview-image">🌈 TRANSPARENT</div>
            <h3>Chroma Key Version</h3>
            <div class="url-box">
              http://localhost:5175?obs=true&transparent=true
            </div>
            <p>Grüner Hintergrund für Chroma Key</p>
          </div>
        </div>
      </div>

      <div class="step">
        <h2>🔧 Schritt 1: Browser Source hinzufügen</h2>
        <ol>
          <li><strong>In OBS Studio:</strong> Rechtsklick in Sources Panel</li>
          <li><strong>Wählen:</strong> "Add" → "Browser Source"</li>
          <li><strong>Name eingeben:</strong> z.B. "Squirtvana Main"</li>
          <li><strong>OK klicken</strong></li>
        </ol>
      </div>

      <div class="step">
        <h2>⚙️ Schritt 2: Browser Source konfigurieren</h2>

        <h3>🎯 Grundeinstellungen:</h3>
        <div class="setting">
          <strong>URL:</strong>
          <span class="url-box">http://localhost:5175</span>
          <button
            class="quick-copy"
            onclick="copyToClipboard('http://localhost:5175')"
          >
            Kopieren
          </button>
        </div>

        <div class="setting"><strong>Width:</strong> 1920</div>

        <div class="setting"><strong>Height:</strong> 1080</div>

        <div class="setting"><strong>FPS:</strong> 30</div>

        <h3>🔧 Erweiterte Einstellungen:</h3>
        <div class="setting">
          ✅ <strong>Shutdown source when not visible:</strong>
          <span style="color: #ef4444">DEAKTIVIERT</span>
        </div>

        <div class="setting">
          ✅ <strong>Refresh browser when scene becomes active:</strong>
          <span style="color: #10b981">AKTIVIERT</span>
        </div>

        <div class="setting">
          ✅ <strong>Control audio via OBS:</strong>
          <span style="color: #10b981">AKTIVIERT</span>
        </div>
      </div>

      <div class="step">
        <h2>🎬 Schritt 3: Empfohlene Szenen Setup</h2>

        <h3>Szene 1: "Stream Main"</h3>
        <ul>
          <li>🎥 Webcam (falls vorhanden)</li>
          <li>🎮 Browser Source: Squirtvana Main (http://localhost:5175)</li>
          <li>🎵 Audio Input (Mikrofon)</li>
        </ul>

        <h3>Szene 2: "App Fullscreen"</h3>
        <ul>
          <li>
            🎮 Browser Source: Squirtvana Vollbild
            (http://localhost:5175?obs=true)
          </li>
        </ul>

        <h3>Szene 3: "Stream + Overlay"</h3>
        <ul>
          <li>🎥 Webcam</li>
          <li>
            📊 Browser Source: Status Overlay
            (http://localhost:5175?view=status&transparent=true)
          </li>
        </ul>
      </div>

      <div class="step">
        <h2>🌟 Schritt 4: Interaktivität aktivieren</h2>

        <div class="tip">
          💡 <strong>Tipp:</strong> Für Klicks und Interaktion in der Browser
          Source
        </div>

        <ol>
          <li><strong>Rechtsklick</strong> auf Browser Source</li>
          <li><strong>Wählen:</strong> "Interact"</li>
          <li>Neues Fenster öffnet sich mit der App</li>
          <li>
            <strong>Hotkey einrichten:</strong> OBS Settings → Hotkeys →
            "Interact with Browser Source"
          </li>
        </ol>
      </div>

      <div class="step">
        <h2>🎨 Schritt 5: Chroma Key Setup (Optional)</h2>

        <p>Für transparente Overlays:</p>

        <ol>
          <li>
            <strong>URL verwenden:</strong>
            http://localhost:5175?transparent=true
          </li>
          <li><strong>Rechtsklick</strong> auf Browser Source → "Filters"</li>
          <li><strong>Filter hinzufügen:</strong> "Chroma Key"</li>
          <li><strong>Farbe auswählen:</strong> Grün (#00FF00)</li>
          <li><strong>Similarity:</strong> 400</li>
          <li><strong>Smoothness:</strong> 80</li>
        </ol>
      </div>

      <div class="step">
        <h2>🔍 Schritt 6: Test & Validierung</h2>

        <div id="connection-tests">
          <h3>📡 Verbindungstests:</h3>
          <p>
            <span class="status-indicator status-warning"></span>Backend Test:
            <span id="backend-test">Teste...</span>
          </p>
          <p>
            <span class="status-indicator status-warning"></span>Frontend Test:
            <span id="frontend-test">Teste...</span>
          </p>
          <p>
            <span class="status-indicator status-warning"></span>OBS WebSocket:
            <span id="websocket-test">Teste...</span>
          </p>
        </div>

        <div class="tip">
          <strong>✅ Checkliste:</strong>
          <ul>
            <li>Browser Source lädt ohne Fehler</li>
            <li>Interaktion funktioniert (Klicks, Eingaben)</li>
            <li>Performance ist flüssig (30 FPS)</li>
            <li>Audio wird korrekt übertragen</li>
            <li>Hotkeys funktionieren</li>
          </ul>
        </div>
      </div>

      <div class="step">
        <h2>🎮 Schnellstart URLs - Bereit zum Kopieren</h2>

        <div class="url-box">
          <strong>Hauptapp:</strong><br />
          http://localhost:5175
          <button
            class="quick-copy"
            onclick="copyToClipboard('http://localhost:5175')"
          >
            📋
          </button>
        </div>

        <div class="url-box">
          <strong>OBS optimiert:</strong><br />
          http://localhost:5175?obs=true
          <button
            class="quick-copy"
            onclick="copyToClipboard('http://localhost:5175?obs=true')"
          >
            📋
          </button>
        </div>

        <div class="url-box">
          <strong>Transparent:</strong><br />
          http://localhost:5175?obs=true&transparent=true
          <button
            class="quick-copy"
            onclick="copyToClipboard('http://localhost:5175?obs=true&transparent=true')"
          >
            📋
          </button>
        </div>

        <div class="url-box">
          <strong>Nur Status:</strong><br />
          http://localhost:5175?view=status
          <button
            class="quick-copy"
            onclick="copyToClipboard('http://localhost:5175?view=status')"
          >
            📋
          </button>
        </div>
      </div>

      <div class="step">
        <h2>🚀 Los geht's!</h2>
        <p style="font-size: 18px; text-align: center">
          <strong>OBS ist bereit!</strong> 🎉<br />
          Erstellen Sie Ihre erste Browser Source mit einer der URLs oben.
        </p>
      </div>
    </div>

    <script>
      function copyToClipboard(text) {
        navigator.clipboard.writeText(text).then(function () {
          alert("URL kopiert: " + text);
        });
      }

      // Connection Tests
      window.onload = function () {
        // Backend Test
        fetch("http://localhost:5000/health")
          .then((response) =>
            response.ok ? response.json() : Promise.reject()
          )
          .then((data) => {
            document.getElementById("backend-test").textContent =
              "✅ Backend erreichbar";
            document.querySelector(
              "#connection-tests .status-indicator"
            ).className = "status-indicator status-good";
          })
          .catch(() => {
            document.getElementById("backend-test").textContent =
              "❌ Backend nicht erreichbar";
            document.querySelector(
              "#connection-tests .status-indicator"
            ).className = "status-indicator status-error";
          });

        // Frontend Test
        fetch("http://localhost:5175")
          .then((response) => (response.ok ? response : Promise.reject()))
          .then(() => {
            document.getElementById("frontend-test").textContent =
              "✅ Frontend erreichbar";
            document.querySelectorAll(
              "#connection-tests .status-indicator"
            )[1].className = "status-indicator status-good";
          })
          .catch(() => {
            document.getElementById("frontend-test").textContent =
              "❌ Frontend nicht erreichbar";
            document.querySelectorAll(
              "#connection-tests .status-indicator"
            )[1].className = "status-indicator status-error";
          });

        // WebSocket Test
        try {
          const ws = new WebSocket("ws://***********:4455");
          ws.onopen = function () {
            document.getElementById("websocket-test").textContent =
              "✅ OBS WebSocket verfügbar";
            document.querySelectorAll(
              "#connection-tests .status-indicator"
            )[2].className = "status-indicator status-good";
            ws.close();
          };
          ws.onerror = function () {
            document.getElementById("websocket-test").textContent =
              "⚠️ OBS WebSocket nicht aktiv";
            document.querySelectorAll(
              "#connection-tests .status-indicator"
            )[2].className = "status-indicator status-warning";
          };
        } catch (e) {
          document.getElementById("websocket-test").textContent =
            "❌ WebSocket Fehler";
          document.querySelectorAll(
            "#connection-tests .status-indicator"
          )[2].className = "status-indicator status-error";
        }
      };
    </script>
  </body>
</html>
