<!DOCTYPE html>
<html lang="de">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>OBS WebSocket Test - Squirtvana</title>
    <style>
      body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        font-family: Arial, sans-serif;
        text-align: center;
        padding: 20px;
        margin: 0;
      }
      .container {
        max-width: 800px;
        margin: 0 auto;
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 20px;
        padding: 30px;
      }
      .status {
        padding: 10px;
        border-radius: 10px;
        margin: 10px 0;
        font-weight: bold;
      }
      .status.connected {
        background: rgba(34, 197, 94, 0.3);
      }
      .status.disconnected {
        background: rgba(239, 68, 68, 0.3);
      }
      .status.authenticating {
        background: rgba(251, 191, 36, 0.3);
      }

      .controls {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
        margin: 20px 0;
      }

      button {
        background: rgba(139, 92, 246, 0.8);
        border: none;
        color: white;
        padding: 15px 20px;
        border-radius: 10px;
        cursor: pointer;
        font-size: 16px;
        transition: all 0.3s;
      }
      button:hover {
        background: rgba(139, 92, 246, 1);
        transform: translateY(-2px);
      }
      button:disabled {
        background: rgba(107, 114, 128, 0.5);
        cursor: not-allowed;
        transform: none;
      }

      .info {
        background: rgba(0, 0, 0, 0.3);
        padding: 15px;
        border-radius: 10px;
        margin: 10px 0;
        text-align: left;
      }

      .log {
        background: rgba(0, 0, 0, 0.5);
        padding: 15px;
        border-radius: 10px;
        text-align: left;
        font-family: monospace;
        max-height: 300px;
        overflow-y: auto;
        margin-top: 20px;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1>🎥 OBS WebSocket Controller</h1>

      <div id="connection-status" class="status disconnected">
        ❌ Nicht verbunden
      </div>

      <div class="info">
        <h3>📡 Verbindungsdaten:</h3>
        <p><strong>Server:</strong> 172.20.10.3:4455</p>
        <p><strong>Passwort:</strong> CxRmLav6P3xijEHG</p>
        <p><strong>Protokoll:</strong> OBS WebSocket v5</p>
      </div>

      <div class="controls">
        <button onclick="connectOBS()">🔗 Mit OBS verbinden</button>
        <button onclick="disconnectOBS()">❌ Trennen</button>
        <button onclick="toggleRecord()" id="recordBtn" disabled>
          🔴 Recording starten
        </button>
        <button onclick="toggleStream()" id="streamBtn" disabled>
          📡 Stream starten
        </button>
        <button onclick="getScenes()" id="scenesBtn" disabled>
          📋 Szenen laden
        </button>
        <button onclick="getStats()" id="statsBtn" disabled>
          📊 Statistiken
        </button>
      </div>

      <div id="scenes" class="info" style="display: none">
        <h3>🎬 Verfügbare Szenen:</h3>
        <div id="scenes-list"></div>
      </div>

      <div id="log" class="log">
        <div><strong>📝 Log:</strong></div>
      </div>
    </div>

    <script>
      let ws = null;
      let connected = false;
      let authenticated = false;
      let requestId = 1;
      let recording = false;
      let streaming = false;

      function log(message) {
        const logDiv = document.getElementById("log");
        const time = new Date().toLocaleTimeString();
        logDiv.innerHTML += `<div>[${time}] ${message}</div>`;
        logDiv.scrollTop = logDiv.scrollHeight;
      }

      function updateStatus(status, className) {
        const statusDiv = document.getElementById("connection-status");
        statusDiv.textContent = status;
        statusDiv.className = `status ${className}`;
      }

      function updateButtons() {
        document.getElementById("recordBtn").disabled = !authenticated;
        document.getElementById("streamBtn").disabled = !authenticated;
        document.getElementById("scenesBtn").disabled = !authenticated;
        document.getElementById("statsBtn").disabled = !authenticated;
      }

      function connectOBS() {
        if (ws) {
          ws.close();
        }

        log("🔗 Verbinde mit OBS WebSocket...");
        updateStatus("🔄 Verbinde...", "authenticating");

        try {
          ws = new WebSocket("ws://172.20.10.3:4455");

          ws.onopen = function () {
            connected = true;
            log("✅ WebSocket verbunden");
            updateStatus("🔄 Authentifiziere...", "authenticating");
          };

          ws.onmessage = function (event) {
            const data = JSON.parse(event.data);
            handleMessage(data);
          };

          ws.onclose = function () {
            connected = false;
            authenticated = false;
            log("❌ Verbindung geschlossen");
            updateStatus("❌ Nicht verbunden", "disconnected");
            updateButtons();
          };

          ws.onerror = function (error) {
            log("🚨 WebSocket Fehler: " + error);
            updateStatus("🚨 Fehler", "disconnected");
          };
        } catch (error) {
          log("❌ Verbindung fehlgeschlagen: " + error);
          updateStatus("❌ Fehler", "disconnected");
        }
      }

      function disconnectOBS() {
        if (ws) {
          ws.close();
          ws = null;
        }
        connected = false;
        authenticated = false;
        updateButtons();
      }

      function handleMessage(data) {
        switch (data.op) {
          case 0: // Hello
            log("👋 Hello von OBS empfangen");
            authenticate();
            break;

          case 2: // Identified
            authenticated = true;
            log("🎯 Erfolgreich authentifiziert!");
            updateStatus("✅ Verbunden & Authentifiziert", "connected");
            updateButtons();
            break;

          case 5: // Event
            handleEvent(data.d);
            break;

          case 7: // RequestResponse
            handleResponse(data.d);
            break;
        }
      }

      function authenticate() {
        // Einfache Authentifizierung für Demo
        const identifyMessage = {
          op: 1, // Identify
          d: {
            rpcVersion: 1,
            authentication: "CxRmLav6P3xijEHG",
            eventSubscriptions: 33, // Alle Events
          },
        };

        send(identifyMessage);
        log("🔐 Authentifizierung gesendet");
      }

      function send(message) {
        if (ws && connected) {
          ws.send(JSON.stringify(message));
        }
      }

      function handleEvent(eventData) {
        log(`📡 Event: ${eventData.eventType}`);

        switch (eventData.eventType) {
          case "RecordStateChanged":
            recording = eventData.eventData.outputActive;
            document.getElementById("recordBtn").textContent = recording
              ? "⏹️ Recording stoppen"
              : "🔴 Recording starten";
            break;

          case "StreamStateChanged":
            streaming = eventData.eventData.outputActive;
            document.getElementById("streamBtn").textContent = streaming
              ? "📡 Stream stoppen"
              : "📡 Stream starten";
            break;
        }
      }

      function handleResponse(responseData) {
        log(`📨 Response: ${responseData.requestType}`);

        if (responseData.requestType === "GetSceneList") {
          displayScenes(responseData.responseData.scenes);
        } else if (responseData.requestType === "GetStats") {
          log(
            `📊 FPS: ${responseData.responseData.activeFps}, CPU: ${responseData.responseData.cpuUsage}%`
          );
        }
      }

      function toggleRecord() {
        if (!authenticated) return;

        const request = {
          op: 6,
          d: {
            requestType: "ToggleRecord",
            requestId: requestId++,
            requestData: {},
          },
        };

        send(request);
        log("🔴 Recording umschalten...");
      }

      function toggleStream() {
        if (!authenticated) return;

        const request = {
          op: 6,
          d: {
            requestType: "ToggleStream",
            requestId: requestId++,
            requestData: {},
          },
        };

        send(request);
        log("📡 Stream umschalten...");
      }

      function getScenes() {
        if (!authenticated) return;

        const request = {
          op: 6,
          d: {
            requestType: "GetSceneList",
            requestId: requestId++,
            requestData: {},
          },
        };

        send(request);
        log("🎬 Szenen werden geladen...");
      }

      function getStats() {
        if (!authenticated) return;

        const request = {
          op: 6,
          d: {
            requestType: "GetStats",
            requestId: requestId++,
            requestData: {},
          },
        };

        send(request);
        log("📊 Statistiken werden geladen...");
      }

      function displayScenes(scenes) {
        const scenesDiv = document.getElementById("scenes");
        const scenesList = document.getElementById("scenes-list");

        scenesList.innerHTML = "";
        scenes.forEach((scene) => {
          const button = document.createElement("button");
          button.textContent = `🎬 ${scene.sceneName}`;
          button.onclick = () => switchToScene(scene.sceneName);
          scenesList.appendChild(button);
        });

        scenesDiv.style.display = "block";
      }

      function switchToScene(sceneName) {
        if (!authenticated) return;

        const request = {
          op: 6,
          d: {
            requestType: "SetCurrentProgramScene",
            requestId: requestId++,
            requestData: {
              sceneName: sceneName,
            },
          },
        };

        send(request);
        log(`🎬 Wechsle zu Szene: ${sceneName}`);
      }

      // Auto-connect beim Laden
      window.onload = function () {
        log("🚀 OBS WebSocket Controller gestartet");
        log('💡 Klicken Sie "Mit OBS verbinden" um zu starten');
      };
    </script>
  </body>
</html>
