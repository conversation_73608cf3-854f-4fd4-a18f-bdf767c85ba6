{
  "recommendations": [
    // Python Development
    "ms-python.python",
    "ms-python.flake8",
    "ms-python.black-formatter",
    "ms-python.debugpy",
    "ms-python.pylint",
    
    // JavaScript/React Development
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "dbaeumer.vscode-eslint",
    "ms-vscode.vscode-typescript-next",
    "formulahendry.auto-rename-tag",
    "christian-kohler.path-intellisense",
    "christian-kohler.npm-intellisense",
    "ms-vscode.vscode-npm-script",
    
    // General Development
    "ms-vscode.vscode-json",
    "redhat.vscode-yaml",
    "ms-vscode.vscode-thunder-client",
    "humao.rest-client",
    "ms-vscode.live-server",
    "ritwickdey.liveserver",
    
    // Git & Version Control
    "eamodio.gitlens",
    "github.vscode-pull-request-github",
    "github.copilot",
    "github.copilot-chat",
    
    // Themes & Icons
    "zhuangtongfa.material-theme",
    "pkief.material-icon-theme",
    "dracula-theme.theme-dracula",
    
    // Productivity
    "ms-vscode-remote.remote-containers",
    "ms-vscode-remote.remote-ssh",
    "ms-vscode.remote-explorer",
    "alefragnani.bookmarks",
    "gruntfuggly.todo-tree",
    "streetsidesoftware.code-spell-checker",
    
    // API Development
    "rangav.vscode-thunder-client",
    "42crunch.vscode-openapi",
    "ms-vscode.rest-client",
    
    // Docker & Containers
    "ms-azuretools.vscode-docker",
    "ms-vscode-remote.remote-containers",
    
    // Markdown & Documentation
    "yzhang.markdown-all-in-one",
    "shd101wyy.markdown-preview-enhanced",
    "davidanson.vscode-markdownlint"
  ],
  "unwantedRecommendations": [
    "ms-vscode.vscode-typescript",
    "hookyqr.beautify"
  ]
}

