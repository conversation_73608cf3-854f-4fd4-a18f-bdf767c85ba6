{"version": "2.0.0", "tasks": [{"label": "🚀 Start Squirtvana (Full Stack)", "type": "shell", "command": "./start-squirtvana.sh", "group": {"kind": "build", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new", "showReuseMessage": false, "clear": true}, "problemMatcher": [], "runOptions": {"runOn": "folderOpen"}}, {"label": "⚛️ Frontend: Install Dependencies", "type": "shell", "command": "npm", "args": ["install"], "options": {"cwd": "${workspaceFolder}/squirtvana-complete-frontend"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": false}, "problemMatcher": ["$npm"]}, {"label": "⚛️ Frontend: Dev Server", "type": "shell", "command": "npm", "args": ["run", "dev"], "options": {"cwd": "${workspaceFolder}/squirtvana-complete-frontend"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new", "showReuseMessage": false}, "problemMatcher": [], "isBackground": true}, {"label": "⚛️ Frontend: Build Production", "type": "shell", "command": "npm", "args": ["run", "build"], "options": {"cwd": "${workspaceFolder}/squirtvana-complete-frontend"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": false}, "problemMatcher": ["$tsc"]}, {"label": "⚛️ Frontend: <PERSON><PERSON>", "type": "shell", "command": "npm", "args": ["run", "lint"], "options": {"cwd": "${workspaceFolder}/squirtvana-complete-frontend"}, "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": false}, "problemMatcher": ["$eslint-stylish"]}, {"label": "🐍 Backend: Create Virtual Environment", "type": "shell", "command": "python3", "args": ["-m", "venv", "venv"], "options": {"cwd": "${workspaceFolder}/squirtvana-backend"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": false}}, {"label": "🐍 Backend: Install Dependencies", "type": "shell", "command": "bash", "args": ["-c", "source venv/bin/activate && pip install -r requirements.txt"], "options": {"cwd": "${workspaceFolder}/squirtvana-backend"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": false}, "dependsOn": "🐍 Backend: Create Virtual Environment"}, {"label": "🐍 Backend: Start Development Server", "type": "shell", "command": "bash", "args": ["-c", "source venv/bin/activate && python src/main.py"], "options": {"cwd": "${workspaceFolder}/squirtvana-backend"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new", "showReuseMessage": false}, "problemMatcher": [], "isBackground": true}, {"label": "🐍 Backend: Run Tests", "type": "shell", "command": "bash", "args": ["-c", "source venv/bin/activate && python -m pytest -v"], "options": {"cwd": "${workspaceFolder}/squirtvana-backend"}, "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": false}, "problemMatcher": []}, {"label": "🐍 Backend: Format Code (Black)", "type": "shell", "command": "bash", "args": ["-c", "source venv/bin/activate && black src/"], "options": {"cwd": "${workspaceFolder}/squirtvana-backend"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": false}}, {"label": "🔧 System Check", "type": "shell", "command": "./system-check.sh", "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": false}}, {"label": "🔧 Install Fedora Dependencies", "type": "shell", "command": "./install-squirtvana-fedora-ultra-robust.sh", "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new", "showReuseMessage": false}, "options": {"shell": {"executable": "bash", "args": ["-c"]}}}, {"label": "🧹 Clean All", "type": "shell", "command": "bash", "args": ["-c", "rm -rf squirtvana-complete-frontend/node_modules squirtvana-complete-frontend/dist squirtvana-backend/venv squirtvana-backend/src/static/*"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": false}}, {"label": "📦 Build & Deploy Frontend to Backend", "type": "shell", "command": "bash", "args": ["-c", "cd squirtvana-complete-frontend && npm run build && cp -r dist/* ../squirtvana-backend/src/static/"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": false}, "dependsOn": "⚛️ Frontend: Build Production"}]}