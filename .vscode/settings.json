{"python.defaultInterpreterPath": "./squirtvana-backend/venv/bin/python", "python.terminal.activateEnvironment": true, "python.linting.enabled": true, "python.linting.pylintEnabled": true, "python.linting.flake8Enabled": true, "python.formatting.provider": "black", "python.formatting.blackArgs": ["--line-length=88"], "python.analysis.typeCheckingMode": "basic", "python.analysis.autoImportCompletions": true, "eslint.workingDirectories": ["squirtvana-complete-frontend"], "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact"], "typescript.preferences.includePackageJsonAutoImports": "auto", "typescript.suggest.autoImports": true, "emmet.includeLanguages": {"javascript": "javascriptreact", "typescript": "typescriptreact"}, "files.associations": {"*.jsx": "javascriptreact", "*.tsx": "typescriptreact", "*.env": "dotenv"}, "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.organizeImports": "explicit"}, "editor.tabSize": 2, "editor.insertSpaces": true, "editor.detectIndentation": true, "[python]": {"editor.tabSize": 4, "editor.insertSpaces": true, "editor.formatOnSave": true, "editor.defaultFormatter": "ms-python.black-formatter"}, "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[javascriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[json]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[html]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[css]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "terminal.integrated.defaultProfile.linux": "bash", "terminal.integrated.cwd": "${workspaceFolder}", "terminal.integrated.env.linux": {"PYTHONPATH": "${workspaceFolder}/squirtvana-backend/src"}, "files.exclude": {"**/node_modules": true, "**/venv": true, "**/__pycache__": true, "**/dist": true, "**/.git": true, "**/*.pyc": true, "**/.pytest_cache": true, "**/.coverage": true}, "search.exclude": {"**/node_modules": true, "**/venv": true, "**/__pycache__": true, "**/dist": true, "**/*.pyc": true}, "git.ignoreLimitWarning": true, "git.autofetch": true, "explorer.fileNesting.enabled": true, "explorer.fileNesting.patterns": {"package.json": "package-lock.json, pnpm-lock.yaml, yarn.lock, .npmrc", "vite.config.js": "vite.config.*.js, vitest.config.js", "tailwind.config.js": "postcss.config.js, tailwind.config.*.js", "requirements.txt": "requirements-dev.txt, requirements-test.txt", ".env": ".env.*, .env.example", "README.md": "README-*.md, CHANGELOG.md, LICENSE, .gitignore"}, "workbench.colorCustomizations": {"titleBar.activeBackground": "#8b5cf6", "titleBar.activeForeground": "#ffffff", "statusBar.background": "#8b5cf6", "statusBar.foreground": "#ffffff", "activityBar.background": "#1e1b4b", "activityBar.foreground": "#ffffff"}, "workbench.iconTheme": "material-icon-theme", "workbench.colorTheme": "One Dark Pro", "tailwindCSS.includeLanguages": {"javascript": "javascript", "javascriptreact": "javascriptreact"}, "tailwindCSS.experimental.classRegex": [["clsx\\(([^)]*)\\)", "(?:'|\"|`)([^']*)(?:'|\"|`)"], ["cn\\(([^)]*)\\)", "(?:'|\"|`)([^']*)(?:'|\"|`)"]]}