import React, { useState, useEffect } from "react";
import { OBSWebSocketClient } from "../utils/obsIntegration";
import { But<PERSON> } from "./ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "./ui/card";
import {
  Play,
  Square,
  Radio,
  RadioIcon as RadioOff,
  Camera,
  Monitor,
  Settings,
  Wifi,
  WifiOff,
} from "lucide-react";

const OBSController = () => {
  const [obsClient, setObsClient] = useState(null);
  const [connected, setConnected] = useState(false);
  const [authenticated, setAuthenticated] = useState(false);
  const [recording, setRecording] = useState(false);
  const [streaming, setStreaming] = useState(false);
  const [currentScene, setCurrentScene] = useState("");

  useEffect(() => {
    // OBS Client initialisieren
    const client = new OBSWebSocketClient(
      "ws://***********:4455",
      "CxRmLav6P3xijEHG"
    );
    setObsClient(client);

    // Event Listeners für Status Updates
    const originalHandleMessage = client.handleMessage.bind(client);
    client.handleMessage = (data) => {
      originalHandleMessage(data);

      switch (data.op) {
        case 2: // Identified
          setAuthenticated(true);
          break;
        case 5: // Event
          handleOBSEvent(data.d);
          break;
      }
    };

    const originalOnOpen = client.ws?.onopen;
    const originalOnClose = client.ws?.onclose;

    // Status Updates
    client.connect();

    return () => {
      if (client.ws) {
        client.ws.close();
      }
    };
  }, []);

  const handleOBSEvent = (eventData) => {
    switch (eventData.eventType) {
      case "RecordStateChanged":
        setRecording(eventData.eventData.outputActive);
        break;
      case "StreamStateChanged":
        setStreaming(eventData.eventData.outputActive);
        break;
      case "CurrentProgramSceneChanged":
        setCurrentScene(eventData.eventData.sceneName);
        break;
    }
  };

  const connectToOBS = () => {
    if (obsClient) {
      obsClient.connect();
      setConnected(true);
    }
  };

  const toggleRecording = () => {
    if (obsClient && authenticated) {
      obsClient.toggleRecord();
    }
  };

  const toggleStreaming = () => {
    if (obsClient && authenticated) {
      obsClient.toggleStream();
    }
  };

  const switchScene = (sceneName) => {
    if (obsClient && authenticated) {
      obsClient.setCurrentProgramScene(sceneName);
    }
  };

  const ConnectionStatus = () => (
    <div className="flex items-center gap-2 mb-4">
      {connected ? (
        authenticated ? (
          <>
            <Wifi className="w-5 h-5 text-green-500" />
            <span className="text-green-500">
              OBS Verbunden & Authentifiziert
            </span>
          </>
        ) : (
          <>
            <WifiOff className="w-5 h-5 text-yellow-500" />
            <span className="text-yellow-500">
              OBS Verbunden (Authentifizierung...)
            </span>
          </>
        )
      ) : (
        <>
          <WifiOff className="w-5 h-5 text-red-500" />
          <span className="text-red-500">OBS Nicht verbunden</span>
        </>
      )}
    </div>
  );

  return (
    <Card className="w-full max-w-md">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Camera className="w-5 h-5" />
          OBS Studio Controller
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <ConnectionStatus />

        {!connected && (
          <Button onClick={connectToOBS} className="w-full">
            <Settings className="w-4 h-4 mr-2" />
            Mit OBS verbinden
          </Button>
        )}

        {authenticated && (
          <>
            <div className="grid grid-cols-2 gap-2">
              <Button
                onClick={toggleRecording}
                variant={recording ? "destructive" : "default"}
                className="flex items-center gap-2"
              >
                {recording ? (
                  <Square className="w-4 h-4" />
                ) : (
                  <Play className="w-4 h-4" />
                )}
                {recording ? "Stop Rec" : "Start Rec"}
              </Button>

              <Button
                onClick={toggleStreaming}
                variant={streaming ? "destructive" : "default"}
                className="flex items-center gap-2"
              >
                {streaming ? (
                  <RadioOff className="w-4 h-4" />
                ) : (
                  <Radio className="w-4 h-4" />
                )}
                {streaming ? "Stop Stream" : "Start Stream"}
              </Button>
            </div>

            <div className="space-y-2">
              <p className="text-sm font-medium">
                Aktuelle Szene: {currentScene || "Keine"}
              </p>

              <div className="grid grid-cols-1 gap-1">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => switchScene("Squirtvana Main")}
                >
                  <Monitor className="w-4 h-4 mr-2" />
                  Squirtvana Main
                </Button>

                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => switchScene("Chat Only")}
                >
                  <Monitor className="w-4 h-4 mr-2" />
                  Chat Only
                </Button>

                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => switchScene("Full Screen App")}
                >
                  <Monitor className="w-4 h-4 mr-2" />
                  Full Screen App
                </Button>
              </div>
            </div>

            <div className="text-xs text-gray-500 space-y-1">
              <p>🎥 OBS IP: ***********:4455</p>
              <p>🔐 Authentifiziert mit Passwort</p>
              <p>📡 WebSocket v5 Protokoll</p>
            </div>
          </>
        )}
      </CardContent>
    </Card>
  );
};

export default OBSController;
