/**
 * OBS Studio Integration Utilities
 */

// OBS Environment Detection
expo// OBS WebSocket Integration 
export class OBSWebSocketClient {
  constructor(address = 'ws://***********:4455', password = 'CxRmLav6P3xijEHG') {
    this.address = address;
    this.password = password;
    this.ws = null;
    this.connected = false;
    this.authenticated = false;
  }t isOBSEnvironment = () => {
  return (
    window.location.search.includes("obs=true") ||
    window.navigator.userAgent.includes("CEF") ||
    window.navigator.userAgent.includes("obs")
  );
};

// URL Parameter Detection
export const getViewMode = () => {
  const params = new URLSearchParams(window.location.search);
  return {
    obs: params.get("obs") === "true",
    transparent: params.get("transparent") === "true",
    view: params.get("view") || "full",
    overlay: params.get("overlay") === "true",
  };
};

// OBS Optimization
export const optimizeForOBS = () => {
  if (isOBSEnvironment()) {
    // Add OBS mode class
    document.body.classList.add("obs-mode");

    // Reduce animations and transitions
    const style = document.createElement("style");
    style.textContent = `
      .obs-mode * { 
        animation-duration: 0.1s !important;
        transition-duration: 0.1s !important;
      }
      .obs-mode .glass-effect {
        backdrop-filter: none !important;
      }
    `;
    document.head.appendChild(style);

    console.log("🎥 OBS Mode aktiviert");
  }
};

// Transparent Background für Chroma Key
export const enableTransparency = () => {
  const { transparent } = getViewMode();
  if (transparent) {
    document.body.style.background = "#00FF00"; // Chroma Key Grün
    document.body.classList.add("obs-transparent");
    console.log("🌈 Transparenz-Modus aktiviert");
  }
};

// View Mode Handler
export const setupViewMode = () => {
  const { view } = getViewMode();

  switch (view) {
    case "controls":
      document.body.classList.add("nur-steuerung");
      break;
    case "status":
      document.body.classList.add("nur-status");
      break;
    case "overlay":
      document.body.classList.add("overlay-mode");
      break;
    default:
      document.body.classList.add("vollbild-app");
  }

  console.log(`📺 View Mode: ${view}`);
};

// OBS WebSocket Integration mit Authentifizierung
export class OBSWebSocketClient {
  constructor(address = "ws://***********:4455", password = "CxRmLav6P3xijEHG") {
    this.address = address;
    this.password = password;
    this.ws = null;
    this.connected = false;
    this.authenticated = false;
    this.requestId = 1;
  }

  connect() {
    try {
      console.log('🔗 Verbinde mit OBS WebSocket:', this.address);
      this.ws = new WebSocket(this.address);

      this.ws.onopen = () => {
        this.connected = true;
        console.log('✅ OBS WebSocket verbunden');
        // OBS WebSocket v5 Authentifizierung
        this.authenticate();
      };

      this.ws.onmessage = (event) => {
        const data = JSON.parse(event.data);
        this.handleMessage(data);
      };

      this.ws.onclose = () => {
        this.connected = false;
        this.authenticated = false;
        console.log('❌ OBS WebSocket getrennt');
      };

      this.ws.onerror = (error) => {
        console.error('🚨 OBS WebSocket Fehler:', error);
      };

    } catch (error) {
      console.warn('⚠️ OBS WebSocket nicht verfügbar:', error);
    }
  }

  async authenticate() {
    if (!this.connected) return;

    // OBS WebSocket v5 Hello Message abwarten
    // Dann Identify Message senden
    const identifyMessage = {
      op: 1, // Identify
      d: {
        rpcVersion: 1,
        authentication: await this.generateAuth(),
        eventSubscriptions: 33 // Alle Events
      }
    };

    this.send(identifyMessage);
  }

  async generateAuth() {
    if (!this.password) return null;

    // Für einfache Demo - in Produktion SHA256 verwenden
    return this.password;
  }

  handleMessage(data) {
    switch (data.op) {
      case 0: // Hello
        console.log('👋 OBS Hello empfangen');
        break;
      case 2: // Identified
        this.authenticated = true;
        console.log('🎯 OBS authentifiziert');
        break;
      case 5: // Event
        console.log('📡 OBS Event:', data.d.eventType);
        break;
      case 7: // RequestResponse
        console.log('📨 OBS Response:', data.d);
        break;
    }
  }

  send(message) {
    if (this.connected && this.ws) {
      this.ws.send(JSON.stringify(message));
    }
  }

  // Szene wechseln
  setCurrentProgramScene(sceneName) {
    if (!this.authenticated) return;

    const request = {
      op: 6, // Request
      d: {
        requestType: "SetCurrentProgramScene",
        requestId: this.requestId++,
        requestData: {
          sceneName: sceneName
        }
      }
    };

    this.send(request);
    console.log(`🎬 Szene gewechselt zu: ${sceneName}`);
  }

  // Quelle ein-/ausblenden
  setSceneItemEnabled(sceneName, sourceId, enabled) {
    if (!this.authenticated) return;

    const request = {
      op: 6,
      d: {
        requestType: "SetSceneItemEnabled",
        requestId: this.requestId++,
        requestData: {
          sceneName: sceneName,
          sceneItemId: sourceId,
          sceneItemEnabled: enabled
        }
      }
    };

    this.send(request);
    console.log(`${enabled ? '👁️' : '🙈'} Quelle ${sourceId} ${enabled ? 'ein' : 'aus'}geblendet`);
  }

  // Recording starten/stoppen
  toggleRecord() {
    if (!this.authenticated) return;

    const request = {
      op: 6,
      d: {
        requestType: "ToggleRecord",
        requestId: this.requestId++,
        requestData: {}
      }
    };

    this.send(request);
    console.log('🔴 Recording umgeschaltet');
  }

  // Streaming starten/stoppen
  toggleStream() {
    if (!this.authenticated) return;

    const request = {
      op: 6,
      d: {
        requestType: "ToggleStream", 
        requestId: this.requestId++,
        requestData: {}
      }
    };

    this.send(request);
    console.log('📡 Stream umgeschaltet');
  }
}
}

// Initialize OBS Integration
export const initOBSIntegration = () => {
  optimizeForOBS();
  enableTransparency();
  setupViewMode();

  // Auto-connect to OBS WebSocket if available
  const obsClient = new OBSWebSocketClient();
  obsClient.connect();

  return obsClient;
};

export default {
  isOBSEnvironment,
  getViewMode,
  optimizeForOBS,
  enableTransparency,
  setupViewMode,
  initOBSIntegration,
  OBSWebSocketClient,
};
