import { useState, useEffect } from "react";
import {
  Mic,
  Play,
  Square,
  Radio,
  Settings,
  Cpu,
  HardDrive,
  MemoryStick,
  Wifi,
  WifiOff,
  MessageSquare,
  Volume2,
  Camera,
  Monitor,
} from "lucide-react";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { apiCall, formatPercentage } from "@/lib/utils";
import { initOBSIntegration, getViewMode } from "@/utils/obsIntegration";
import OBSController from "@/components/OBSController";

function App() {
  // State management
  const [prompt, setPrompt] = useState("");
  const [generatedText, setGeneratedText] = useState("");
  const [isGenerating, setIsGenerating] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  const [obsMode, setObsMode] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [isStreaming, setIsStreaming] = useState(false);
  const [selectedScene, setSelectedScene] = useState("cam1");
  const [systemStats, setSystemStats] = useState({
    cpu: 0,
    memory: 0,
    disk: 0,
  });
  const [connectionStatus, setConnectionStatus] = useState({
    backend: false,
    obs: false,
    telegram: false,
  });

  // Available OBS scenes
  const obsScenes = [
    { value: "cam1", label: "Cam 1" },
    { value: "pussy_closeup", label: "Pussy Closeup" },
    { value: "full_body", label: "Full Body" },
    { value: "face_cam", label: "Face Cam" },
    { value: "toys", label: "Toys Scene" },
    { value: "bed", label: "Bed Scene" },
  ];

  // Initialize OBS Integration
  useEffect(() => {
    const initOBS = async () => {
      const result = await initOBSIntegration();
      setObsMode(result.viewMode === "obs");
    };

    initOBS();
  }, []);

  // Check system status
  useEffect(() => {
    const checkStatus = async () => {
      try {
        // Check backend health
        const health = await apiCall("/health");
        setConnectionStatus((prev) => ({ ...prev, backend: true }));

        // Get system stats
        const stats = await apiCall("/system/stats");
        setSystemStats({
          cpu: stats.cpu || Math.random() * 100,
          memory: stats.memory || Math.random() * 100,
          disk: stats.disk || Math.random() * 100,
        });

        // Check OBS status
        const obsStatus = await apiCall("/obs/status");
        setConnectionStatus((prev) => ({ ...prev, obs: obsStatus.connected }));

        // Check Telegram status
        const telegramStatus = await apiCall("/system/telegram-status");
        setConnectionStatus((prev) => ({
          ...prev,
          telegram: telegramStatus.active,
        }));
      } catch (error) {
        console.error("Status check failed:", error);
        setConnectionStatus((prev) => ({ ...prev, backend: false }));
      }
    };

    checkStatus();
    const interval = setInterval(checkStatus, 5000);
    return () => clearInterval(interval);
  }, []);

  // Generate DirtyTalk response
  const handleGenerateResponse = async () => {
    if (!prompt.trim()) return;

    setIsGenerating(true);
    try {
      const response = await apiCall("/gpt/generate", {
        method: "POST",
        body: JSON.stringify({ prompt }),
      });

      if (response.success) {
        setGeneratedText(response.generated_text);

        // Update OBS text source
        await apiCall("/obs/update-text", {
          method: "POST",
          body: JSON.stringify({
            source: "DirtyTalk",
            text: response.generated_text,
          }),
        });
      }
    } catch (error) {
      console.error("Generation failed:", error);
      setGeneratedText("Error: Failed to generate response");
    } finally {
      setIsGenerating(false);
    }
  };

  // Generate and play audio
  const handleGenerateAudio = async () => {
    if (!generatedText) return;

    try {
      const response = await apiCall("/audio/generate", {
        method: "POST",
        body: JSON.stringify({ text: generatedText }),
      });

      if (response.success) {
        // Audio will be available at the returned URL
        const audio = new Audio(response.audio_url);
        audio.play();
      }
    } catch (error) {
      console.error("Audio generation failed:", error);
    }
  };

  // Play test voice
  const handleTestVoice = async () => {
    setIsPlaying(true);
    try {
      const response = await apiCall("/audio/test-voice", {
        method: "POST",
      });

      if (response.success) {
        const audio = new Audio(response.audio_url);
        audio.play();
        audio.onended = () => setIsPlaying(false);
      }
    } catch (error) {
      console.error("Test voice failed:", error);
      setIsPlaying(false);
    }
  };

  // Change OBS scene
  const handleSceneChange = async (sceneValue) => {
    setSelectedScene(sceneValue);
    try {
      await apiCall("/obs/change-scene", {
        method: "POST",
        body: JSON.stringify({ scene: sceneValue }),
      });
    } catch (error) {
      console.error("Scene change failed:", error);
    }
  };

  // Toggle streaming
  const handleToggleStream = async () => {
    try {
      const endpoint = isStreaming ? "/stream/stop" : "/stream/start";
      const response = await apiCall(endpoint, { method: "POST" });

      if (response.success) {
        setIsStreaming(!isStreaming);
      }
    } catch (error) {
      console.error("Stream toggle failed:", error);
    }
  };

  // Toggle recording
  const handleToggleRecording = async () => {
    try {
      const endpoint = isRecording ? "/recording/stop" : "/recording/start";
      const response = await apiCall(endpoint, { method: "POST" });

      if (response.success) {
        setIsRecording(!isRecording);
      }
    } catch (error) {
      console.error("Recording toggle failed:", error);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 p-4">
      <div className="max-w-md mx-auto space-y-6">
        {/* Header */}
        <div className="text-center text-white">
          <h1 className="text-3xl font-bold bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
            Squirtvana
          </h1>
          <p className="text-purple-200 text-sm">Mobile Control Center</p>
        </div>

        {/* System Status */}
        <Card className="bg-gray-900/50 border-purple-500/30 backdrop-blur-sm">
          <CardHeader className="pb-3">
            <CardTitle className="text-white text-lg flex items-center gap-2">
              <Monitor className="w-5 h-5" />
              System Status
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            {/* Connection Status */}
            <div className="flex justify-between items-center">
              <span className="text-gray-300 text-sm">Connections</span>
              <div className="flex gap-2">
                <div
                  className={`status-indicator ${
                    connectionStatus.backend
                      ? "status-online"
                      : "status-offline"
                  }`}
                  title="Backend"
                />
                <div
                  className={`status-indicator ${
                    connectionStatus.obs ? "status-online" : "status-offline"
                  }`}
                  title="OBS"
                />
                <div
                  className={`status-indicator ${
                    connectionStatus.telegram
                      ? "status-online"
                      : "status-offline"
                  }`}
                  title="Telegram"
                />
              </div>
            </div>

            {/* System Stats */}
            <div className="grid grid-cols-3 gap-4 text-center">
              <div className="space-y-1">
                <Cpu className="w-4 h-4 mx-auto text-blue-400" />
                <div className="text-xs text-gray-400">CPU</div>
                <div className="text-sm font-semibold text-white">
                  {formatPercentage(systemStats.cpu)}
                </div>
              </div>
              <div className="space-y-1">
                <MemoryStick className="w-4 h-4 mx-auto text-green-400" />
                <div className="text-xs text-gray-400">RAM</div>
                <div className="text-sm font-semibold text-white">
                  {formatPercentage(systemStats.memory)}
                </div>
              </div>
              <div className="space-y-1">
                <HardDrive className="w-4 h-4 mx-auto text-yellow-400" />
                <div className="text-xs text-gray-400">Disk</div>
                <div className="text-sm font-semibold text-white">
                  {formatPercentage(systemStats.disk)}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* DirtyTalk Generator */}
        <Card className="bg-gray-900/50 border-purple-500/30 backdrop-blur-sm">
          <CardHeader className="pb-3">
            <CardTitle className="text-white text-lg flex items-center gap-2">
              <MessageSquare className="w-5 h-5" />
              DirtyTalk Generator
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <Textarea
              placeholder="Enter your prompt for AI generation..."
              value={prompt}
              onChange={(e) => setPrompt(e.target.value)}
              className="bg-gray-800 border-gray-600 text-white placeholder-gray-400 min-h-[100px]"
            />

            <Button
              onClick={handleGenerateResponse}
              disabled={isGenerating || !prompt.trim()}
              className="w-full bg-purple-600 hover:bg-purple-700 text-white"
            >
              {isGenerating ? (
                <>
                  <Settings className="w-4 h-4 mr-2 animate-spin" />
                  Generating...
                </>
              ) : (
                <>
                  <MessageSquare className="w-4 h-4 mr-2" />
                  Generate Response
                </>
              )}
            </Button>

            {generatedText && (
              <div className="p-3 bg-gray-800 rounded-md border border-gray-600">
                <p className="text-white text-sm">{generatedText}</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Audio Controls */}
        <Card className="bg-gray-900/50 border-purple-500/30 backdrop-blur-sm">
          <CardHeader className="pb-3">
            <CardTitle className="text-white text-lg flex items-center gap-2">
              <Volume2 className="w-5 h-5" />
              Audio Controls
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="grid grid-cols-2 gap-3">
              <Button
                onClick={handleGenerateAudio}
                disabled={!generatedText}
                className="bg-pink-600 hover:bg-pink-700 text-white"
              >
                <Mic className="w-4 h-4 mr-2" />
                Generate Audio
              </Button>

              <Button
                onClick={handleTestVoice}
                disabled={isPlaying}
                className="bg-green-600 hover:bg-green-700 text-white"
              >
                <Play className="w-4 h-4 mr-2" />
                {isPlaying ? "Playing..." : "Test Voice"}
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* OBS Controls */}
        <Card className="bg-gray-900/50 border-purple-500/30 backdrop-blur-sm">
          <CardHeader className="pb-3">
            <CardTitle className="text-white text-lg flex items-center gap-2">
              <Camera className="w-5 h-5" />
              OBS Controls
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Scene Selection */}
            <div className="space-y-2">
              <label className="text-sm text-gray-300">Scene Selection</label>
              <Select
                value={selectedScene}
                onValueChange={handleSceneChange}
                className="bg-gray-800 border-gray-600 text-white"
              >
                <SelectContent>
                  {obsScenes.map((scene) => (
                    <SelectItem key={scene.value} value={scene.value}>
                      {scene.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Stream & Recording Controls */}
            <div className="grid grid-cols-2 gap-3">
              <Button
                onClick={handleToggleStream}
                className={`${
                  isStreaming
                    ? "bg-red-600 hover:bg-red-700"
                    : "bg-blue-600 hover:bg-blue-700"
                } text-white`}
              >
                {isStreaming ? (
                  <>
                    <Square className="w-4 h-4 mr-2" />
                    Stop Stream
                  </>
                ) : (
                  <>
                    <Radio className="w-4 h-4 mr-2" />
                    Start Stream
                  </>
                )}
              </Button>

              <Button
                onClick={handleToggleRecording}
                className={`${
                  isRecording
                    ? "bg-red-600 hover:bg-red-700"
                    : "bg-orange-600 hover:bg-orange-700"
                } text-white`}
              >
                {isRecording ? (
                  <>
                    <Square className="w-4 h-4 mr-2" />
                    Stop Rec
                  </>
                ) : (
                  <>
                    <Radio className="w-4 h-4 mr-2" />
                    Start Rec
                  </>
                )}
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* OBS Controller */}
        <OBSController />

        {/* Footer */}
        <div className="text-center text-purple-300 text-xs">
          <p>Squirtvana PWA v1.0 - Mobile Streaming Control</p>
          <p className="flex items-center justify-center gap-1 mt-1">
            {connectionStatus.backend ? (
              <>
                <Wifi className="w-3 h-3" />
                Connected
              </>
            ) : (
              <>
                <WifiOff className="w-3 h-3" />
                Disconnected
              </>
            )}
          </p>
        </div>
      </div>
    </div>
  );
}

export default App;
