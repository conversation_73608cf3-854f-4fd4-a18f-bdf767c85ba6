{"version": 3, "names": ["pluginNameMap", "asyncDoExpressions", "syntax", "name", "url", "decimal", "decorators", "transform", "doExpressions", "exportDefaultFrom", "flow", "functionBind", "functionSent", "jsx", "pipelineOperator", "recordAndTuple", "throwExpressions", "typescript", "Object", "assign", "asyncGenerators", "classProperties", "classPrivateProperties", "classPrivateMethods", "classStaticBlock", "dynamicImport", "exportNamespaceFrom", "importAssertions", "importAttributes", "importMeta", "logicalAssignment", "moduleStringNames", "numericSeparator", "nullishCoalescingOperator", "objectRestSpread", "optionalCatchBinding", "optionalChaining", "privateIn", "regexpUnicodeSets", "getNameURLCombination", "generateMissingPluginMessage", "missing<PERSON><PERSON><PERSON><PERSON><PERSON>", "loc", "codeFrame", "filename", "helpMessage", "line", "column", "pluginInfo", "syntaxPlugin", "transformPlugin", "syntaxPluginInfo", "transformPluginInfo", "sectionType", "startsWith", "msgFilename"], "sources": ["../../../src/parser/util/missing-plugin-helper.ts"], "sourcesContent": ["const pluginNameMap: Record<\n  string,\n  Partial<Record<\"syntax\" | \"transform\", Record<\"name\" | \"url\", string>>>\n> = {\n  asyncDoExpressions: {\n    syntax: {\n      name: \"@babel/plugin-syntax-async-do-expressions\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-async-do-expressions\",\n    },\n  },\n  decimal: {\n    syntax: {\n      name: \"@babel/plugin-syntax-decimal\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-decimal\",\n    },\n  },\n  decorators: {\n    syntax: {\n      name: \"@babel/plugin-syntax-decorators\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-decorators\",\n    },\n    transform: {\n      name: \"@babel/plugin-proposal-decorators\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-proposal-decorators\",\n    },\n  },\n  doExpressions: {\n    syntax: {\n      name: \"@babel/plugin-syntax-do-expressions\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-do-expressions\",\n    },\n    transform: {\n      name: \"@babel/plugin-proposal-do-expressions\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-proposal-do-expressions\",\n    },\n  },\n  exportDefaultFrom: {\n    syntax: {\n      name: \"@babel/plugin-syntax-export-default-from\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-export-default-from\",\n    },\n    transform: {\n      name: \"@babel/plugin-proposal-export-default-from\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-proposal-export-default-from\",\n    },\n  },\n  flow: {\n    syntax: {\n      name: \"@babel/plugin-syntax-flow\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-flow\",\n    },\n    transform: {\n      name: \"@babel/preset-flow\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-preset-flow\",\n    },\n  },\n  functionBind: {\n    syntax: {\n      name: \"@babel/plugin-syntax-function-bind\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-function-bind\",\n    },\n    transform: {\n      name: \"@babel/plugin-proposal-function-bind\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-proposal-function-bind\",\n    },\n  },\n  functionSent: {\n    syntax: {\n      name: \"@babel/plugin-syntax-function-sent\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-function-sent\",\n    },\n    transform: {\n      name: \"@babel/plugin-proposal-function-sent\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-proposal-function-sent\",\n    },\n  },\n  jsx: {\n    syntax: {\n      name: \"@babel/plugin-syntax-jsx\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-jsx\",\n    },\n    transform: {\n      name: \"@babel/preset-react\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-preset-react\",\n    },\n  },\n  pipelineOperator: {\n    syntax: {\n      name: \"@babel/plugin-syntax-pipeline-operator\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-pipeline-operator\",\n    },\n    transform: {\n      name: \"@babel/plugin-proposal-pipeline-operator\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-proposal-pipeline-operator\",\n    },\n  },\n  recordAndTuple: {\n    syntax: {\n      name: \"@babel/plugin-syntax-record-and-tuple\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-record-and-tuple\",\n    },\n  },\n  throwExpressions: {\n    syntax: {\n      name: \"@babel/plugin-syntax-throw-expressions\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-throw-expressions\",\n    },\n    transform: {\n      name: \"@babel/plugin-proposal-throw-expressions\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-proposal-throw-expressions\",\n    },\n  },\n  typescript: {\n    syntax: {\n      name: \"@babel/plugin-syntax-typescript\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-typescript\",\n    },\n    transform: {\n      name: \"@babel/preset-typescript\",\n      url: \"https://github.com/babel/babel/tree/main/packages/babel-preset-typescript\",\n    },\n  },\n};\n\nif (!process.env.BABEL_8_BREAKING) {\n  // TODO: This plugins are now supported by default by @babel/parser.\n  Object.assign(pluginNameMap, {\n    asyncGenerators: {\n      syntax: {\n        name: \"@babel/plugin-syntax-async-generators\",\n        url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-async-generators\",\n      },\n      transform: {\n        name: \"@babel/plugin-transform-async-generator-functions\",\n        url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-transform-async-generator-functions\",\n      },\n    },\n    classProperties: {\n      syntax: {\n        name: \"@babel/plugin-syntax-class-properties\",\n        url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-class-properties\",\n      },\n      transform: {\n        name: \"@babel/plugin-transform-class-properties\",\n        url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-transform-class-properties\",\n      },\n    },\n    classPrivateProperties: {\n      syntax: {\n        name: \"@babel/plugin-syntax-class-properties\",\n        url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-class-properties\",\n      },\n      transform: {\n        name: \"@babel/plugin-transform-class-properties\",\n        url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-transform-class-properties\",\n      },\n    },\n    classPrivateMethods: {\n      syntax: {\n        name: \"@babel/plugin-syntax-class-properties\",\n        url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-class-properties\",\n      },\n      transform: {\n        name: \"@babel/plugin-transform-private-methods\",\n        url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-transform-private-methods\",\n      },\n    },\n    classStaticBlock: {\n      syntax: {\n        name: \"@babel/plugin-syntax-class-static-block\",\n        url: \"https://github.com/babel/babel/tree/HEAD/packages/babel-plugin-syntax-class-static-block\",\n      },\n      transform: {\n        name: \"@babel/plugin-transform-class-static-block\",\n        url: \"https://github.com/babel/babel/tree/HEAD/packages/babel-plugin-transform-class-static-block\",\n      },\n    },\n    dynamicImport: {\n      syntax: {\n        name: \"@babel/plugin-syntax-dynamic-import\",\n        url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-dynamic-import\",\n      },\n    },\n    exportNamespaceFrom: {\n      syntax: {\n        name: \"@babel/plugin-syntax-export-namespace-from\",\n        url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-export-namespace-from\",\n      },\n      transform: {\n        name: \"@babel/plugin-transform-export-namespace-from\",\n        url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-transform-export-namespace-from\",\n      },\n    },\n    // Will be removed\n    importAssertions: {\n      syntax: {\n        name: \"@babel/plugin-syntax-import-assertions\",\n        url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-import-assertions\",\n      },\n    },\n    importAttributes: {\n      syntax: {\n        name: \"@babel/plugin-syntax-import-attributes\",\n        url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-import-attributes\",\n      },\n    },\n    importMeta: {\n      syntax: {\n        name: \"@babel/plugin-syntax-import-meta\",\n        url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-import-meta\",\n      },\n    },\n    logicalAssignment: {\n      syntax: {\n        name: \"@babel/plugin-syntax-logical-assignment-operators\",\n        url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-logical-assignment-operators\",\n      },\n      transform: {\n        name: \"@babel/plugin-transform-logical-assignment-operators\",\n        url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-transform-logical-assignment-operators\",\n      },\n    },\n    moduleStringNames: {\n      syntax: {\n        name: \"@babel/plugin-syntax-module-string-names\",\n        url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-module-string-names\",\n      },\n    },\n    numericSeparator: {\n      syntax: {\n        name: \"@babel/plugin-syntax-numeric-separator\",\n        url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-numeric-separator\",\n      },\n      transform: {\n        name: \"@babel/plugin-transform-numeric-separator\",\n        url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-transform-numeric-separator\",\n      },\n    },\n    nullishCoalescingOperator: {\n      syntax: {\n        name: \"@babel/plugin-syntax-nullish-coalescing-operator\",\n        url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-nullish-coalescing-operator\",\n      },\n      transform: {\n        name: \"@babel/plugin-transform-nullish-coalescing-operator\",\n        url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-transform-nullish-coalescing-opearator\",\n      },\n    },\n    objectRestSpread: {\n      syntax: {\n        name: \"@babel/plugin-syntax-object-rest-spread\",\n        url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-object-rest-spread\",\n      },\n      transform: {\n        name: \"@babel/plugin-transform-object-rest-spread\",\n        url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-transform-object-rest-spread\",\n      },\n    },\n    optionalCatchBinding: {\n      syntax: {\n        name: \"@babel/plugin-syntax-optional-catch-binding\",\n        url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-optional-catch-binding\",\n      },\n      transform: {\n        name: \"@babel/plugin-transform-optional-catch-binding\",\n        url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-transform-optional-catch-binding\",\n      },\n    },\n    optionalChaining: {\n      syntax: {\n        name: \"@babel/plugin-syntax-optional-chaining\",\n        url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-optional-chaining\",\n      },\n      transform: {\n        name: \"@babel/plugin-transform-optional-chaining\",\n        url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-transform-optional-chaining\",\n      },\n    },\n    privateIn: {\n      syntax: {\n        name: \"@babel/plugin-syntax-private-property-in-object\",\n        url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-syntax-private-property-in-object\",\n      },\n      transform: {\n        name: \"@babel/plugin-transform-private-property-in-object\",\n        url: \"https://github.com/babel/babel/tree/main/packages/babel-plugin-transform-private-property-in-object\",\n      },\n    },\n    regexpUnicodeSets: {\n      syntax: {\n        name: \"@babel/plugin-syntax-unicode-sets-regex\",\n        url: \"https://github.com/babel/babel/blob/main/packages/babel-plugin-syntax-unicode-sets-regex/README.md\",\n      },\n      transform: {\n        name: \"@babel/plugin-transform-unicode-sets-regex\",\n        url: \"https://github.com/babel/babel/blob/main/packages/babel-plugin-proposalunicode-sets-regex/README.md\",\n      },\n    },\n  });\n}\n\nconst getNameURLCombination = ({ name, url }: { name: string; url: string }) =>\n  `${name} (${url})`;\n\n/*\nReturns a string of the format:\nSupport for the experimental syntax [@babel/parser plugin name] isn't currently enabled ([loc]):\n\n[code frame]\n\nAdd [npm package name] ([url]) to the 'plugins' section of your Babel config\nto enable [parsing|transformation].\n*/\nexport default function generateMissingPluginMessage(\n  missingPluginName: string,\n  loc: {\n    line: number;\n    column: number;\n  },\n  codeFrame: string,\n  filename: string,\n): string {\n  let helpMessage =\n    `Support for the experimental syntax '${missingPluginName}' isn't currently enabled ` +\n    `(${loc.line}:${loc.column + 1}):\\n\\n` +\n    codeFrame;\n  const pluginInfo = pluginNameMap[missingPluginName];\n  if (pluginInfo) {\n    const { syntax: syntaxPlugin, transform: transformPlugin } = pluginInfo;\n    if (syntaxPlugin) {\n      const syntaxPluginInfo = getNameURLCombination(syntaxPlugin);\n      if (transformPlugin) {\n        const transformPluginInfo = getNameURLCombination(transformPlugin);\n        const sectionType = transformPlugin.name.startsWith(\"@babel/plugin\")\n          ? \"plugins\"\n          : \"presets\";\n        helpMessage += `\\n\\nAdd ${transformPluginInfo} to the '${sectionType}' section of your Babel config to enable transformation.\nIf you want to leave it as-is, add ${syntaxPluginInfo} to the 'plugins' section to enable parsing.`;\n      } else {\n        helpMessage +=\n          `\\n\\nAdd ${syntaxPluginInfo} to the 'plugins' section of your Babel config ` +\n          `to enable parsing.`;\n      }\n    }\n  }\n\n  const msgFilename =\n    filename === \"unknown\" ? \"<name of the input file>\" : filename;\n  helpMessage += `\n\nIf you already added the plugin for this syntax to your config, it's possible that your config \\\nisn't being loaded.\nYou can re-run Babel with the BABEL_SHOW_CONFIG_FOR environment variable to show the loaded \\\nconfiguration:\n\\tnpx cross-env BABEL_SHOW_CONFIG_FOR=${msgFilename} <your build command>\nSee https://babeljs.io/docs/configuration#print-effective-configs for more info.\n`;\n  return helpMessage;\n}\n"], "mappings": ";;;;;;AAAA,MAAMA,aAGL,GAAG;EACFC,kBAAkB,EAAE;IAClBC,MAAM,EAAE;MACNC,IAAI,EAAE,2CAA2C;MACjDC,GAAG,EAAE;IACP;EACF,CAAC;EACDC,OAAO,EAAE;IACPH,MAAM,EAAE;MACNC,IAAI,EAAE,8BAA8B;MACpCC,GAAG,EAAE;IACP;EACF,CAAC;EACDE,UAAU,EAAE;IACVJ,MAAM,EAAE;MACNC,IAAI,EAAE,iCAAiC;MACvCC,GAAG,EAAE;IACP,CAAC;IACDG,SAAS,EAAE;MACTJ,IAAI,EAAE,mCAAmC;MACzCC,GAAG,EAAE;IACP;EACF,CAAC;EACDI,aAAa,EAAE;IACbN,MAAM,EAAE;MACNC,IAAI,EAAE,qCAAqC;MAC3CC,GAAG,EAAE;IACP,CAAC;IACDG,SAAS,EAAE;MACTJ,IAAI,EAAE,uCAAuC;MAC7CC,GAAG,EAAE;IACP;EACF,CAAC;EACDK,iBAAiB,EAAE;IACjBP,MAAM,EAAE;MACNC,IAAI,EAAE,0CAA0C;MAChDC,GAAG,EAAE;IACP,CAAC;IACDG,SAAS,EAAE;MACTJ,IAAI,EAAE,4CAA4C;MAClDC,GAAG,EAAE;IACP;EACF,CAAC;EACDM,IAAI,EAAE;IACJR,MAAM,EAAE;MACNC,IAAI,EAAE,2BAA2B;MACjCC,GAAG,EAAE;IACP,CAAC;IACDG,SAAS,EAAE;MACTJ,IAAI,EAAE,oBAAoB;MAC1BC,GAAG,EAAE;IACP;EACF,CAAC;EACDO,YAAY,EAAE;IACZT,MAAM,EAAE;MACNC,IAAI,EAAE,oCAAoC;MAC1CC,GAAG,EAAE;IACP,CAAC;IACDG,SAAS,EAAE;MACTJ,IAAI,EAAE,sCAAsC;MAC5CC,GAAG,EAAE;IACP;EACF,CAAC;EACDQ,YAAY,EAAE;IACZV,MAAM,EAAE;MACNC,IAAI,EAAE,oCAAoC;MAC1CC,GAAG,EAAE;IACP,CAAC;IACDG,SAAS,EAAE;MACTJ,IAAI,EAAE,sCAAsC;MAC5CC,GAAG,EAAE;IACP;EACF,CAAC;EACDS,GAAG,EAAE;IACHX,MAAM,EAAE;MACNC,IAAI,EAAE,0BAA0B;MAChCC,GAAG,EAAE;IACP,CAAC;IACDG,SAAS,EAAE;MACTJ,IAAI,EAAE,qBAAqB;MAC3BC,GAAG,EAAE;IACP;EACF,CAAC;EACDU,gBAAgB,EAAE;IAChBZ,MAAM,EAAE;MACNC,IAAI,EAAE,wCAAwC;MAC9CC,GAAG,EAAE;IACP,CAAC;IACDG,SAAS,EAAE;MACTJ,IAAI,EAAE,0CAA0C;MAChDC,GAAG,EAAE;IACP;EACF,CAAC;EACDW,cAAc,EAAE;IACdb,MAAM,EAAE;MACNC,IAAI,EAAE,uCAAuC;MAC7CC,GAAG,EAAE;IACP;EACF,CAAC;EACDY,gBAAgB,EAAE;IAChBd,MAAM,EAAE;MACNC,IAAI,EAAE,wCAAwC;MAC9CC,GAAG,EAAE;IACP,CAAC;IACDG,SAAS,EAAE;MACTJ,IAAI,EAAE,0CAA0C;MAChDC,GAAG,EAAE;IACP;EACF,CAAC;EACDa,UAAU,EAAE;IACVf,MAAM,EAAE;MACNC,IAAI,EAAE,iCAAiC;MACvCC,GAAG,EAAE;IACP,CAAC;IACDG,SAAS,EAAE;MACTJ,IAAI,EAAE,0BAA0B;MAChCC,GAAG,EAAE;IACP;EACF;AACF,CAAC;AAEkC;EAEjCc,MAAM,CAACC,MAAM,CAACnB,aAAa,EAAE;IAC3BoB,eAAe,EAAE;MACflB,MAAM,EAAE;QACNC,IAAI,EAAE,uCAAuC;QAC7CC,GAAG,EAAE;MACP,CAAC;MACDG,SAAS,EAAE;QACTJ,IAAI,EAAE,mDAAmD;QACzDC,GAAG,EAAE;MACP;IACF,CAAC;IACDiB,eAAe,EAAE;MACfnB,MAAM,EAAE;QACNC,IAAI,EAAE,uCAAuC;QAC7CC,GAAG,EAAE;MACP,CAAC;MACDG,SAAS,EAAE;QACTJ,IAAI,EAAE,0CAA0C;QAChDC,GAAG,EAAE;MACP;IACF,CAAC;IACDkB,sBAAsB,EAAE;MACtBpB,MAAM,EAAE;QACNC,IAAI,EAAE,uCAAuC;QAC7CC,GAAG,EAAE;MACP,CAAC;MACDG,SAAS,EAAE;QACTJ,IAAI,EAAE,0CAA0C;QAChDC,GAAG,EAAE;MACP;IACF,CAAC;IACDmB,mBAAmB,EAAE;MACnBrB,MAAM,EAAE;QACNC,IAAI,EAAE,uCAAuC;QAC7CC,GAAG,EAAE;MACP,CAAC;MACDG,SAAS,EAAE;QACTJ,IAAI,EAAE,yCAAyC;QAC/CC,GAAG,EAAE;MACP;IACF,CAAC;IACDoB,gBAAgB,EAAE;MAChBtB,MAAM,EAAE;QACNC,IAAI,EAAE,yCAAyC;QAC/CC,GAAG,EAAE;MACP,CAAC;MACDG,SAAS,EAAE;QACTJ,IAAI,EAAE,4CAA4C;QAClDC,GAAG,EAAE;MACP;IACF,CAAC;IACDqB,aAAa,EAAE;MACbvB,MAAM,EAAE;QACNC,IAAI,EAAE,qCAAqC;QAC3CC,GAAG,EAAE;MACP;IACF,CAAC;IACDsB,mBAAmB,EAAE;MACnBxB,MAAM,EAAE;QACNC,IAAI,EAAE,4CAA4C;QAClDC,GAAG,EAAE;MACP,CAAC;MACDG,SAAS,EAAE;QACTJ,IAAI,EAAE,+CAA+C;QACrDC,GAAG,EAAE;MACP;IACF,CAAC;IAEDuB,gBAAgB,EAAE;MAChBzB,MAAM,EAAE;QACNC,IAAI,EAAE,wCAAwC;QAC9CC,GAAG,EAAE;MACP;IACF,CAAC;IACDwB,gBAAgB,EAAE;MAChB1B,MAAM,EAAE;QACNC,IAAI,EAAE,wCAAwC;QAC9CC,GAAG,EAAE;MACP;IACF,CAAC;IACDyB,UAAU,EAAE;MACV3B,MAAM,EAAE;QACNC,IAAI,EAAE,kCAAkC;QACxCC,GAAG,EAAE;MACP;IACF,CAAC;IACD0B,iBAAiB,EAAE;MACjB5B,MAAM,EAAE;QACNC,IAAI,EAAE,mDAAmD;QACzDC,GAAG,EAAE;MACP,CAAC;MACDG,SAAS,EAAE;QACTJ,IAAI,EAAE,sDAAsD;QAC5DC,GAAG,EAAE;MACP;IACF,CAAC;IACD2B,iBAAiB,EAAE;MACjB7B,MAAM,EAAE;QACNC,IAAI,EAAE,0CAA0C;QAChDC,GAAG,EAAE;MACP;IACF,CAAC;IACD4B,gBAAgB,EAAE;MAChB9B,MAAM,EAAE;QACNC,IAAI,EAAE,wCAAwC;QAC9CC,GAAG,EAAE;MACP,CAAC;MACDG,SAAS,EAAE;QACTJ,IAAI,EAAE,2CAA2C;QACjDC,GAAG,EAAE;MACP;IACF,CAAC;IACD6B,yBAAyB,EAAE;MACzB/B,MAAM,EAAE;QACNC,IAAI,EAAE,kDAAkD;QACxDC,GAAG,EAAE;MACP,CAAC;MACDG,SAAS,EAAE;QACTJ,IAAI,EAAE,qDAAqD;QAC3DC,GAAG,EAAE;MACP;IACF,CAAC;IACD8B,gBAAgB,EAAE;MAChBhC,MAAM,EAAE;QACNC,IAAI,EAAE,yCAAyC;QAC/CC,GAAG,EAAE;MACP,CAAC;MACDG,SAAS,EAAE;QACTJ,IAAI,EAAE,4CAA4C;QAClDC,GAAG,EAAE;MACP;IACF,CAAC;IACD+B,oBAAoB,EAAE;MACpBjC,MAAM,EAAE;QACNC,IAAI,EAAE,6CAA6C;QACnDC,GAAG,EAAE;MACP,CAAC;MACDG,SAAS,EAAE;QACTJ,IAAI,EAAE,gDAAgD;QACtDC,GAAG,EAAE;MACP;IACF,CAAC;IACDgC,gBAAgB,EAAE;MAChBlC,MAAM,EAAE;QACNC,IAAI,EAAE,wCAAwC;QAC9CC,GAAG,EAAE;MACP,CAAC;MACDG,SAAS,EAAE;QACTJ,IAAI,EAAE,2CAA2C;QACjDC,GAAG,EAAE;MACP;IACF,CAAC;IACDiC,SAAS,EAAE;MACTnC,MAAM,EAAE;QACNC,IAAI,EAAE,iDAAiD;QACvDC,GAAG,EAAE;MACP,CAAC;MACDG,SAAS,EAAE;QACTJ,IAAI,EAAE,oDAAoD;QAC1DC,GAAG,EAAE;MACP;IACF,CAAC;IACDkC,iBAAiB,EAAE;MACjBpC,MAAM,EAAE;QACNC,IAAI,EAAE,yCAAyC;QAC/CC,GAAG,EAAE;MACP,CAAC;MACDG,SAAS,EAAE;QACTJ,IAAI,EAAE,4CAA4C;QAClDC,GAAG,EAAE;MACP;IACF;EACF,CAAC,CAAC;AACJ;AAEA,MAAMmC,qBAAqB,GAAGA,CAAC;EAAEpC,IAAI;EAAEC;AAAmC,CAAC,KACzE,GAAGD,IAAI,KAAKC,GAAG,GAAG;AAWL,SAASoC,4BAA4BA,CAClDC,iBAAyB,EACzBC,GAGC,EACDC,SAAiB,EACjBC,QAAgB,EACR;EACR,IAAIC,WAAW,GACb,wCAAwCJ,iBAAiB,4BAA4B,GACrF,IAAIC,GAAG,CAACI,IAAI,IAAIJ,GAAG,CAACK,MAAM,GAAG,CAAC,QAAQ,GACtCJ,SAAS;EACX,MAAMK,UAAU,GAAGhD,aAAa,CAACyC,iBAAiB,CAAC;EACnD,IAAIO,UAAU,EAAE;IACd,MAAM;MAAE9C,MAAM,EAAE+C,YAAY;MAAE1C,SAAS,EAAE2C;IAAgB,CAAC,GAAGF,UAAU;IACvE,IAAIC,YAAY,EAAE;MAChB,MAAME,gBAAgB,GAAGZ,qBAAqB,CAACU,YAAY,CAAC;MAC5D,IAAIC,eAAe,EAAE;QACnB,MAAME,mBAAmB,GAAGb,qBAAqB,CAACW,eAAe,CAAC;QAClE,MAAMG,WAAW,GAAGH,eAAe,CAAC/C,IAAI,CAACmD,UAAU,CAAC,eAAe,CAAC,GAChE,SAAS,GACT,SAAS;QACbT,WAAW,IAAI,WAAWO,mBAAmB,YAAYC,WAAW;AAC5E,qCAAqCF,gBAAgB,8CAA8C;MAC7F,CAAC,MAAM;QACLN,WAAW,IACT,WAAWM,gBAAgB,iDAAiD,GAC5E,oBAAoB;MACxB;IACF;EACF;EAEA,MAAMI,WAAW,GACfX,QAAQ,KAAK,SAAS,GAAG,0BAA0B,GAAGA,QAAQ;EAChEC,WAAW,IAAI;AACjB;AACA;AACA;AACA;AACA;AACA,wCAAwCU,WAAW;AACnD;AACA,CAAC;EACC,OAAOV,WAAW;AACpB;AAAC", "ignoreList": []}