import{r as e,a as r}from"./vendor-D7amQhf0.js";import{r as t,C as o,M as n,a as s,b as a,H as i,c as l,S as c,V as d,d as u,P as p,e as f,f as m,R as b,W as g,g as h}from"./icons-Ce1bakAH.js";!function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const e of document.querySelectorAll('link[rel="modulepreload"]'))r(e);new MutationObserver(e=>{for(const t of e)if("childList"===t.type)for(const e of t.addedNodes)"LINK"===e.tagName&&"modulepreload"===e.rel&&r(e)}).observe(document,{childList:!0,subtree:!0})}function r(e){if(e.ep)return;e.ep=!0;const r=function(e){const r={};return e.integrity&&(r.integrity=e.integrity),e.referrerPolicy&&(r.referrerPolicy=e.referrerPolicy),"use-credentials"===e.crossOrigin?r.credentials="include":"anonymous"===e.crossOrigin?r.credentials="omit":r.credentials="same-origin",r}(e);fetch(e.href,r)}}();var x,y,v={exports:{}},w={};var j,N=(y||(y=1,v.exports=function(){if(x)return w;x=1;var r=e(),t=Symbol.for("react.element"),o=Symbol.for("react.fragment"),n=Object.prototype.hasOwnProperty,s=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,a={key:!0,ref:!0,__self:!0,__source:!0};function i(e,r,o){var i,l={},c=null,d=null;for(i in void 0!==o&&(c=""+o),void 0!==r.key&&(c=""+r.key),void 0!==r.ref&&(d=r.ref),r)n.call(r,i)&&!a.hasOwnProperty(i)&&(l[i]=r[i]);if(e&&e.defaultProps)for(i in r=e.defaultProps)void 0===l[i]&&(l[i]=r[i]);return{$$typeof:t,type:e,key:c,ref:d,props:l,_owner:s.current}}return w.Fragment=o,w.jsx=i,w.jsxs=i,w}()),v.exports),k={};var S=function(){if(j)return k;j=1;var e=r();return k.createRoot=e.createRoot,k.hydrateRoot=e.hydrateRoot,k}();function C(e){var r,t,o="";if("string"==typeof e||"number"==typeof e)o+=e;else if("object"==typeof e)if(Array.isArray(e)){var n=e.length;for(r=0;r<n;r++)e[r]&&(t=C(e[r]))&&(o&&(o+=" "),o+=t)}else for(t in e)e[t]&&(o&&(o+=" "),o+=t);return o}function z(){for(var e,r,t=0,o="";t<arguments.length;)(e=arguments[t++])&&(r=P(e))&&(o&&(o+=" "),o+=r);return o}function P(e){if("string"==typeof e)return e;for(var r,t="",o=0;o<e.length;o++)e[o]&&(r=P(e[o]))&&(t&&(t+=" "),t+=r);return t}function R(e){var r=function(e){var r=e.theme,t=e.prefix,o={nextPart:new Map,validators:[]},n=function(e,r){if(!r)return e;return e.map(function(e){return[e[0],e[1].map(function(e){return"string"==typeof e?r+e:"object"==typeof e?Object.fromEntries(Object.entries(e).map(function(e){var t=e[0],o=e[1];return[r+t,o]})):e})]})}(Object.entries(e.classGroups),t);return n.forEach(function(e){var t=e[0];I(e[1],o,t,r)}),o}(e),t=e.conflictingClassGroups,o=e.conflictingClassGroupModifiers,n=void 0===o?{}:o;return{getClassGroupId:function(e){var t=e.split("-");return""===t[0]&&1!==t.length&&t.shift(),O(t,r)||function(e){if(T.test(e)){var r=T.exec(e)[1],t=null==r?void 0:r.substring(0,r.indexOf(":"));if(t)return"arbitrary.."+t}}(e)},getConflictingClassGroupIds:function(e,r){var o=t[e]||[];return r&&n[e]?[].concat(o,n[e]):o}}}function O(e,r){var t;if(0===e.length)return r.classGroupId;var o=e[0],n=r.nextPart.get(o),s=n?O(e.slice(1),n):void 0;if(s)return s;if(0!==r.validators.length){var a=e.join("-");return null==(t=r.validators.find(function(e){return(0,e.validator)(a)}))?void 0:t.classGroupId}}var T=/^\[(.+)\]$/;function I(e,r,t,o){e.forEach(function(e){if("string"!=typeof e){if("function"==typeof e)return e.isThemeGetter?void I(e(o),r,t,o):void r.validators.push({validator:e,classGroupId:t});Object.entries(e).forEach(function(e){var n=e[0];I(e[1],M(r,n),t,o)})}else{(""===e?r:M(r,e)).classGroupId=t}})}function M(e,r){var t=e;return r.split("-").forEach(function(e){t.nextPart.has(e)||t.nextPart.set(e,{nextPart:new Map,validators:[]}),t=t.nextPart.get(e)}),t}function _(e){if(e<1)return{get:function(){},set:function(){}};var r=0,t=new Map,o=new Map;function n(n,s){t.set(n,s),++r>e&&(r=0,o=t,t=new Map)}return{get:function(e){var r=t.get(e);return void 0!==r?r:void 0!==(r=o.get(e))?(n(e,r),r):void 0},set:function(e,r){t.has(e)?t.set(e,r):n(e,r)}}}function G(e){var r=e.separator||":",t=1===r.length,o=r[0],n=r.length;return function(e){for(var s,a=[],i=0,l=0,c=0;c<e.length;c++){var d=e[c];if(0===i){if(d===o&&(t||e.slice(c,c+n)===r)){a.push(e.slice(l,c)),l=c+n;continue}if("/"===d){s=c;continue}}"["===d?i++:"]"===d&&i--}var u=0===a.length?e:e.substring(l),p=u.startsWith("!");return{modifiers:a,hasImportantModifier:p,baseClassName:p?u.substring(1):u,maybePostfixModifierPosition:s&&s>l?s-l:void 0}}}var E=/\s+/;function A(){for(var e=arguments.length,r=new Array(e),t=0;t<e;t++)r[t]=arguments[t];var o,n,s,a=function(e){var t=r[0],l=r.slice(1).reduce(function(e,r){return r(e)},t());return o=function(e){return{cache:_(e.cacheSize),splitModifiers:G(e),...R(e)}}(l),n=o.cache.get,s=o.cache.set,a=i,i(e)};function i(e){var r=n(e);if(r)return r;var t=function(e,r){var t=r.splitModifiers,o=r.getClassGroupId,n=r.getConflictingClassGroupIds,s=new Set;return e.trim().split(E).map(function(e){var r=t(e),n=r.modifiers,s=r.hasImportantModifier,a=r.baseClassName,i=r.maybePostfixModifierPosition,l=o(i?a.substring(0,i):a),c=Boolean(i);if(!l){if(!i)return{isTailwindClass:!1,originalClassName:e};if(!(l=o(a)))return{isTailwindClass:!1,originalClassName:e};c=!1}var d=function(e){if(e.length<=1)return e;var r=[],t=[];return e.forEach(function(e){"["===e[0]?(r.push.apply(r,t.sort().concat([e])),t=[]):t.push(e)}),r.push.apply(r,t.sort()),r}(n).join(":");return{isTailwindClass:!0,modifierId:s?d+"!":d,classGroupId:l,originalClassName:e,hasPostfixModifier:c}}).reverse().filter(function(e){if(!e.isTailwindClass)return!0;var r=e.modifierId,t=e.classGroupId,o=e.hasPostfixModifier,a=r+t;return!s.has(a)&&(s.add(a),n(t,o).forEach(function(e){return s.add(r+e)}),!0)}).reverse().map(function(e){return e.originalClassName}).join(" ")}(e,o);return s(e,t),t}return function(){return a(z.apply(null,arguments))}}function F(e){var r=function(r){return r[e]||[]};return r.isThemeGetter=!0,r}var $=/^\[(?:([a-z-]+):)?(.+)\]$/i,B=/^\d+\/\d+$/,W=new Set(["px","full","screen"]),D=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,L=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,q=/^-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/;function J(e){return Q(e)||W.has(e)||B.test(e)||H(e)}function H(e){return ne(e,"length",se)}function U(e){return ne(e,"size",ae)}function V(e){return ne(e,"position",ae)}function K(e){return ne(e,"url",ie)}function Y(e){return ne(e,"number",Q)}function Q(e){return!Number.isNaN(Number(e))}function X(e){return e.endsWith("%")&&Q(e.slice(0,-1))}function Z(e){return le(e)||ne(e,"number",le)}function ee(e){return $.test(e)}function re(){return!0}function te(e){return D.test(e)}function oe(e){return ne(e,"",ce)}function ne(e,r,t){var o=$.exec(e);return!!o&&(o[1]?o[1]===r:t(o[2]))}function se(e){return L.test(e)}function ae(){return!1}function ie(e){return e.startsWith("url(")}function le(e){return Number.isInteger(Number(e))}function ce(e){return q.test(e)}function de(){var e=F("colors"),r=F("spacing"),t=F("blur"),o=F("brightness"),n=F("borderColor"),s=F("borderRadius"),a=F("borderSpacing"),i=F("borderWidth"),l=F("contrast"),c=F("grayscale"),d=F("hueRotate"),u=F("invert"),p=F("gap"),f=F("gradientColorStops"),m=F("gradientColorStopPositions"),b=F("inset"),g=F("margin"),h=F("opacity"),x=F("padding"),y=F("saturate"),v=F("scale"),w=F("sepia"),j=F("skew"),N=F("space"),k=F("translate"),S=function(){return["auto",ee,r]},C=function(){return[ee,r]},z=function(){return["",J]},P=function(){return["auto",Q,ee]},R=function(){return["","0",ee]},O=function(){return[Q,Y]},T=function(){return[Q,ee]};return{cacheSize:500,theme:{colors:[re],spacing:[J],blur:["none","",te,ee],brightness:O(),borderColor:[e],borderRadius:["none","","full",te,ee],borderSpacing:C(),borderWidth:z(),contrast:O(),grayscale:R(),hueRotate:T(),invert:R(),gap:C(),gradientColorStops:[e],gradientColorStopPositions:[X,H],inset:S(),margin:S(),opacity:O(),padding:C(),saturate:O(),scale:O(),sepia:R(),skew:T(),space:C(),translate:C()},classGroups:{aspect:[{aspect:["auto","square","video",ee]}],container:["container"],columns:[{columns:[te]}],"break-after":[{"break-after":["auto","avoid","all","avoid-page","page","left","right","column"]}],"break-before":[{"break-before":["auto","avoid","all","avoid-page","page","left","right","column"]}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none"]}],clear:[{clear:["left","right","both","none"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[].concat(["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],[ee])}],overflow:[{overflow:["auto","hidden","clip","visible","scroll"]}],"overflow-x":[{"overflow-x":["auto","hidden","clip","visible","scroll"]}],"overflow-y":[{"overflow-y":["auto","hidden","clip","visible","scroll"]}],overscroll:[{overscroll:["auto","contain","none"]}],"overscroll-x":[{"overscroll-x":["auto","contain","none"]}],"overscroll-y":[{"overscroll-y":["auto","contain","none"]}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[b]}],"inset-x":[{"inset-x":[b]}],"inset-y":[{"inset-y":[b]}],start:[{start:[b]}],end:[{end:[b]}],top:[{top:[b]}],right:[{right:[b]}],bottom:[{bottom:[b]}],left:[{left:[b]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",Z]}],basis:[{basis:S()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",ee]}],grow:[{grow:R()}],shrink:[{shrink:R()}],order:[{order:["first","last","none",Z]}],"grid-cols":[{"grid-cols":[re]}],"col-start-end":[{col:["auto",{span:["full",Z]},ee]}],"col-start":[{"col-start":P()}],"col-end":[{"col-end":P()}],"grid-rows":[{"grid-rows":[re]}],"row-start-end":[{row:["auto",{span:[Z]},ee]}],"row-start":[{"row-start":P()}],"row-end":[{"row-end":P()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",ee]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",ee]}],gap:[{gap:[p]}],"gap-x":[{"gap-x":[p]}],"gap-y":[{"gap-y":[p]}],"justify-content":[{justify:["normal"].concat(["start","end","center","between","around","evenly","stretch"])}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal"].concat(["start","end","center","between","around","evenly","stretch"],["baseline"])}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[].concat(["start","end","center","between","around","evenly","stretch"],["baseline"])}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[x]}],px:[{px:[x]}],py:[{py:[x]}],ps:[{ps:[x]}],pe:[{pe:[x]}],pt:[{pt:[x]}],pr:[{pr:[x]}],pb:[{pb:[x]}],pl:[{pl:[x]}],m:[{m:[g]}],mx:[{mx:[g]}],my:[{my:[g]}],ms:[{ms:[g]}],me:[{me:[g]}],mt:[{mt:[g]}],mr:[{mr:[g]}],mb:[{mb:[g]}],ml:[{ml:[g]}],"space-x":[{"space-x":[N]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[N]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit",ee,r]}],"min-w":[{"min-w":["min","max","fit",ee,J]}],"max-w":[{"max-w":["0","none","full","min","max","fit","prose",{screen:[te]},te,ee]}],h:[{h:[ee,r,"auto","min","max","fit"]}],"min-h":[{"min-h":["min","max","fit",ee,J]}],"max-h":[{"max-h":[ee,r,"min","max","fit"]}],"font-size":[{text:["base",te,H]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",Y]}],"font-family":[{font:[re]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractons"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",ee]}],"line-clamp":[{"line-clamp":["none",Q,Y]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",ee,J]}],"list-image":[{"list-image":["none",ee]}],"list-style-type":[{list:["none","disc","decimal",ee]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[h]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[h]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[].concat(["solid","dashed","dotted","double","none"],["wavy"])}],"text-decoration-thickness":[{decoration:["auto","from-font",J]}],"underline-offset":[{"underline-offset":["auto",ee,J]}],"text-decoration-color":[{decoration:[e]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],indent:[{indent:C()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",ee]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",ee]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[h]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[].concat(["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],[V])}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",U]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},K]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[m]}],"gradient-via-pos":[{via:[m]}],"gradient-to-pos":[{to:[m]}],"gradient-from":[{from:[f]}],"gradient-via":[{via:[f]}],"gradient-to":[{to:[f]}],rounded:[{rounded:[s]}],"rounded-s":[{"rounded-s":[s]}],"rounded-e":[{"rounded-e":[s]}],"rounded-t":[{"rounded-t":[s]}],"rounded-r":[{"rounded-r":[s]}],"rounded-b":[{"rounded-b":[s]}],"rounded-l":[{"rounded-l":[s]}],"rounded-ss":[{"rounded-ss":[s]}],"rounded-se":[{"rounded-se":[s]}],"rounded-ee":[{"rounded-ee":[s]}],"rounded-es":[{"rounded-es":[s]}],"rounded-tl":[{"rounded-tl":[s]}],"rounded-tr":[{"rounded-tr":[s]}],"rounded-br":[{"rounded-br":[s]}],"rounded-bl":[{"rounded-bl":[s]}],"border-w":[{border:[i]}],"border-w-x":[{"border-x":[i]}],"border-w-y":[{"border-y":[i]}],"border-w-s":[{"border-s":[i]}],"border-w-e":[{"border-e":[i]}],"border-w-t":[{"border-t":[i]}],"border-w-r":[{"border-r":[i]}],"border-w-b":[{"border-b":[i]}],"border-w-l":[{"border-l":[i]}],"border-opacity":[{"border-opacity":[h]}],"border-style":[{border:[].concat(["solid","dashed","dotted","double","none"],["hidden"])}],"divide-x":[{"divide-x":[i]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[i]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[h]}],"divide-style":[{divide:["solid","dashed","dotted","double","none"]}],"border-color":[{border:[n]}],"border-color-x":[{"border-x":[n]}],"border-color-y":[{"border-y":[n]}],"border-color-t":[{"border-t":[n]}],"border-color-r":[{"border-r":[n]}],"border-color-b":[{"border-b":[n]}],"border-color-l":[{"border-l":[n]}],"divide-color":[{divide:[n]}],"outline-style":[{outline:[""].concat(["solid","dashed","dotted","double","none"])}],"outline-offset":[{"outline-offset":[ee,J]}],"outline-w":[{outline:[J]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:z()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[h]}],"ring-offset-w":[{"ring-offset":[J]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:["","inner","none",te,oe]}],"shadow-color":[{shadow:[re]}],opacity:[{opacity:[h]}],"mix-blend":[{"mix-blend":["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity","plus-lighter"]}],"bg-blend":[{"bg-blend":["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity","plus-lighter"]}],filter:[{filter:["","none"]}],blur:[{blur:[t]}],brightness:[{brightness:[o]}],contrast:[{contrast:[l]}],"drop-shadow":[{"drop-shadow":["","none",te,ee]}],grayscale:[{grayscale:[c]}],"hue-rotate":[{"hue-rotate":[d]}],invert:[{invert:[u]}],saturate:[{saturate:[y]}],sepia:[{sepia:[w]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[t]}],"backdrop-brightness":[{"backdrop-brightness":[o]}],"backdrop-contrast":[{"backdrop-contrast":[l]}],"backdrop-grayscale":[{"backdrop-grayscale":[c]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[d]}],"backdrop-invert":[{"backdrop-invert":[u]}],"backdrop-opacity":[{"backdrop-opacity":[h]}],"backdrop-saturate":[{"backdrop-saturate":[y]}],"backdrop-sepia":[{"backdrop-sepia":[w]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[a]}],"border-spacing-x":[{"border-spacing-x":[a]}],"border-spacing-y":[{"border-spacing-y":[a]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",ee]}],duration:[{duration:T()}],ease:[{ease:["linear","in","out","in-out",ee]}],delay:[{delay:T()}],animate:[{animate:["none","spin","ping","pulse","bounce",ee]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[v]}],"scale-x":[{"scale-x":[v]}],"scale-y":[{"scale-y":[v]}],rotate:[{rotate:[Z,ee]}],"translate-x":[{"translate-x":[k]}],"translate-y":[{"translate-y":[k]}],"skew-x":[{"skew-x":[j]}],"skew-y":[{"skew-y":[j]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",ee]}],accent:[{accent:["auto",e]}],appearance:["appearance-none"],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",ee]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":C()}],"scroll-mx":[{"scroll-mx":C()}],"scroll-my":[{"scroll-my":C()}],"scroll-ms":[{"scroll-ms":C()}],"scroll-me":[{"scroll-me":C()}],"scroll-mt":[{"scroll-mt":C()}],"scroll-mr":[{"scroll-mr":C()}],"scroll-mb":[{"scroll-mb":C()}],"scroll-ml":[{"scroll-ml":C()}],"scroll-p":[{"scroll-p":C()}],"scroll-px":[{"scroll-px":C()}],"scroll-py":[{"scroll-py":C()}],"scroll-ps":[{"scroll-ps":C()}],"scroll-pe":[{"scroll-pe":C()}],"scroll-pt":[{"scroll-pt":C()}],"scroll-pr":[{"scroll-pr":C()}],"scroll-pb":[{"scroll-pb":C()}],"scroll-pl":[{"scroll-pl":C()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","pinch-zoom","manipulation",{pan:["x","left","right","y","up","down"]}]}],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",ee]}],fill:[{fill:[e,"none"]}],"stroke-w":[{stroke:[J,Y]}],stroke:[{stroke:[e,"none"]}],sr:["sr-only","not-sr-only"]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}}var ue=A(de);function pe(...e){return ue(function(){for(var e,r,t=0,o="",n=arguments.length;t<n;t++)(e=arguments[t])&&(r=C(e))&&(o&&(o+=" "),o+=r);return o}(e))}const fe="localhost"===window.location.hostname?"http://localhost:5000/api":"/api";async function me(e,r={}){try{const t=await fetch(`${fe}${e}`,{headers:{"Content-Type":"application/json",...r.headers},...r});if(!t.ok)throw new Error(`HTTP error! status: ${t.status}`);return await t.json()}catch(t){throw console.error("API call failed:",t),t}}function be(e){return`${Math.round(e)}%`}const ge={default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},he={default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"},xe=t.forwardRef(({className:e,variant:r="default",size:t="default",...o},n)=>N.jsx("button",{className:pe("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 touch-target",ge[r],he[t],e),ref:n,...o}));xe.displayName="Button";const ye=t.forwardRef(({className:e,...r},t)=>N.jsx("div",{ref:t,className:pe("rounded-lg border bg-card text-card-foreground shadow-sm",e),...r}));ye.displayName="Card";const ve=t.forwardRef(({className:e,...r},t)=>N.jsx("div",{ref:t,className:pe("flex flex-col space-y-1.5 p-6",e),...r}));ve.displayName="CardHeader";const we=t.forwardRef(({className:e,...r},t)=>N.jsx("h3",{ref:t,className:pe("text-2xl font-semibold leading-none tracking-tight",e),...r}));we.displayName="CardTitle";t.forwardRef(({className:e,...r},t)=>N.jsx("p",{ref:t,className:pe("text-sm text-muted-foreground",e),...r})).displayName="CardDescription";const je=t.forwardRef(({className:e,...r},t)=>N.jsx("div",{ref:t,className:pe("p-6 pt-0",e),...r}));je.displayName="CardContent";t.forwardRef(({className:e,...r},t)=>N.jsx("div",{ref:t,className:pe("flex items-center p-6 pt-0",e),...r})).displayName="CardFooter";const Ne=t.forwardRef(({className:e,children:r,...t},n)=>N.jsxs("div",{className:"relative",children:[N.jsx("select",{className:pe("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 appearance-none touch-target",e),ref:n,...t,children:r}),N.jsx(o,{className:"absolute right-3 top-3 h-4 w-4 opacity-50 pointer-events-none"})]}));Ne.displayName="Select";const ke=({children:e})=>e,Se=t.forwardRef(({className:e,children:r,...t},o)=>N.jsx("option",{ref:o,className:pe("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...t,children:r}));Se.displayName="SelectItem";const Ce=t.forwardRef(({className:e,...r},t)=>N.jsx("textarea",{className:pe("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 resize-none",e),ref:t,...r}));function ze(){const[e,r]=t.useState(""),[o,x]=t.useState(""),[y,v]=t.useState(!1),[w,j]=t.useState(!1),[k,S]=t.useState(!1),[C,z]=t.useState(!1),[P,R]=t.useState("cam1"),[O,T]=t.useState({cpu:0,memory:0,disk:0}),[I,M]=t.useState({backend:!1,obs:!1,telegram:!1});t.useEffect(()=>{const e=async()=>{try{await me("/health");M(e=>({...e,backend:!0}));const e=await me("/system/stats");T({cpu:e.cpu||100*Math.random(),memory:e.memory||100*Math.random(),disk:e.disk||100*Math.random()});const r=await me("/obs/status");M(e=>({...e,obs:r.connected}));const t=await me("/system/telegram-status");M(e=>({...e,telegram:t.active}))}catch(e){console.error("Status check failed:",e),M(e=>({...e,backend:!1}))}};e();const r=setInterval(e,5e3);return()=>clearInterval(r)},[]);return N.jsx("div",{className:"min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 p-4",children:N.jsxs("div",{className:"max-w-md mx-auto space-y-6",children:[N.jsxs("div",{className:"text-center text-white",children:[N.jsx("h1",{className:"text-3xl font-bold bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent",children:"Squirtvana"}),N.jsx("p",{className:"text-purple-200 text-sm",children:"Mobile Control Center"})]}),N.jsxs(ye,{className:"bg-gray-900/50 border-purple-500/30 backdrop-blur-sm",children:[N.jsx(ve,{className:"pb-3",children:N.jsxs(we,{className:"text-white text-lg flex items-center gap-2",children:[N.jsx(n,{className:"w-5 h-5"}),"System Status"]})}),N.jsxs(je,{className:"space-y-3",children:[N.jsxs("div",{className:"flex justify-between items-center",children:[N.jsx("span",{className:"text-gray-300 text-sm",children:"Connections"}),N.jsxs("div",{className:"flex gap-2",children:[N.jsx("div",{className:"status-indicator "+(I.backend?"status-online":"status-offline"),title:"Backend"}),N.jsx("div",{className:"status-indicator "+(I.obs?"status-online":"status-offline"),title:"OBS"}),N.jsx("div",{className:"status-indicator "+(I.telegram?"status-online":"status-offline"),title:"Telegram"})]})]}),N.jsxs("div",{className:"grid grid-cols-3 gap-4 text-center",children:[N.jsxs("div",{className:"space-y-1",children:[N.jsx(s,{className:"w-4 h-4 mx-auto text-blue-400"}),N.jsx("div",{className:"text-xs text-gray-400",children:"CPU"}),N.jsx("div",{className:"text-sm font-semibold text-white",children:be(O.cpu)})]}),N.jsxs("div",{className:"space-y-1",children:[N.jsx(a,{className:"w-4 h-4 mx-auto text-green-400"}),N.jsx("div",{className:"text-xs text-gray-400",children:"RAM"}),N.jsx("div",{className:"text-sm font-semibold text-white",children:be(O.memory)})]}),N.jsxs("div",{className:"space-y-1",children:[N.jsx(i,{className:"w-4 h-4 mx-auto text-yellow-400"}),N.jsx("div",{className:"text-xs text-gray-400",children:"Disk"}),N.jsx("div",{className:"text-sm font-semibold text-white",children:be(O.disk)})]})]})]})]}),N.jsxs(ye,{className:"bg-gray-900/50 border-purple-500/30 backdrop-blur-sm",children:[N.jsx(ve,{className:"pb-3",children:N.jsxs(we,{className:"text-white text-lg flex items-center gap-2",children:[N.jsx(l,{className:"w-5 h-5"}),"DirtyTalk Generator"]})}),N.jsxs(je,{className:"space-y-4",children:[N.jsx(Ce,{placeholder:"Enter your prompt for AI generation...",value:e,onChange:e=>r(e.target.value),className:"bg-gray-800 border-gray-600 text-white placeholder-gray-400 min-h-[100px]"}),N.jsx(xe,{onClick:async()=>{if(e.trim()){v(!0);try{const r=await me("/gpt/generate",{method:"POST",body:JSON.stringify({prompt:e})});r.success&&(x(r.generated_text),await me("/obs/update-text",{method:"POST",body:JSON.stringify({source:"DirtyTalk",text:r.generated_text})}))}catch(r){console.error("Generation failed:",r),x("Error: Failed to generate response")}finally{v(!1)}}},disabled:y||!e.trim(),className:"w-full bg-purple-600 hover:bg-purple-700 text-white",children:y?N.jsxs(N.Fragment,{children:[N.jsx(c,{className:"w-4 h-4 mr-2 animate-spin"}),"Generating..."]}):N.jsxs(N.Fragment,{children:[N.jsx(l,{className:"w-4 h-4 mr-2"}),"Generate Response"]})}),o&&N.jsx("div",{className:"p-3 bg-gray-800 rounded-md border border-gray-600",children:N.jsx("p",{className:"text-white text-sm",children:o})})]})]}),N.jsxs(ye,{className:"bg-gray-900/50 border-purple-500/30 backdrop-blur-sm",children:[N.jsx(ve,{className:"pb-3",children:N.jsxs(we,{className:"text-white text-lg flex items-center gap-2",children:[N.jsx(d,{className:"w-5 h-5"}),"Audio Controls"]})}),N.jsx(je,{className:"space-y-3",children:N.jsxs("div",{className:"grid grid-cols-2 gap-3",children:[N.jsxs(xe,{onClick:async()=>{if(o)try{const e=await me("/audio/generate",{method:"POST",body:JSON.stringify({text:o})});if(e.success){new Audio(e.audio_url).play()}}catch(e){console.error("Audio generation failed:",e)}},disabled:!o,className:"bg-pink-600 hover:bg-pink-700 text-white",children:[N.jsx(u,{className:"w-4 h-4 mr-2"}),"Generate Audio"]}),N.jsxs(xe,{onClick:async()=>{j(!0);try{const e=await me("/audio/test-voice",{method:"POST"});if(e.success){const r=new Audio(e.audio_url);r.play(),r.onended=()=>j(!1)}}catch(e){console.error("Test voice failed:",e),j(!1)}},disabled:w,className:"bg-green-600 hover:bg-green-700 text-white",children:[N.jsx(p,{className:"w-4 h-4 mr-2"}),w?"Playing...":"Test Voice"]})]})})]}),N.jsxs(ye,{className:"bg-gray-900/50 border-purple-500/30 backdrop-blur-sm",children:[N.jsx(ve,{className:"pb-3",children:N.jsxs(we,{className:"text-white text-lg flex items-center gap-2",children:[N.jsx(f,{className:"w-5 h-5"}),"OBS Controls"]})}),N.jsxs(je,{className:"space-y-4",children:[N.jsxs("div",{className:"space-y-2",children:[N.jsx("label",{className:"text-sm text-gray-300",children:"Scene Selection"}),N.jsx(Ne,{value:P,onValueChange:async e=>{R(e);try{await me("/obs/change-scene",{method:"POST",body:JSON.stringify({scene:e})})}catch(r){console.error("Scene change failed:",r)}},className:"bg-gray-800 border-gray-600 text-white",children:N.jsx(ke,{children:[{value:"cam1",label:"Cam 1"},{value:"pussy_closeup",label:"Pussy Closeup"},{value:"full_body",label:"Full Body"},{value:"face_cam",label:"Face Cam"},{value:"toys",label:"Toys Scene"},{value:"bed",label:"Bed Scene"}].map(e=>N.jsx(Se,{value:e.value,children:e.label},e.value))})})]}),N.jsxs("div",{className:"grid grid-cols-2 gap-3",children:[N.jsx(xe,{onClick:async()=>{try{const e=C?"/stream/stop":"/stream/start";(await me(e,{method:"POST"})).success&&z(!C)}catch(e){console.error("Stream toggle failed:",e)}},className:(C?"bg-red-600 hover:bg-red-700":"bg-blue-600 hover:bg-blue-700")+" text-white",children:C?N.jsxs(N.Fragment,{children:[N.jsx(m,{className:"w-4 h-4 mr-2"}),"Stop Stream"]}):N.jsxs(N.Fragment,{children:[N.jsx(b,{className:"w-4 h-4 mr-2"}),"Start Stream"]})}),N.jsx(xe,{onClick:async()=>{try{const e=k?"/recording/stop":"/recording/start";(await me(e,{method:"POST"})).success&&S(!k)}catch(e){console.error("Recording toggle failed:",e)}},className:(k?"bg-red-600 hover:bg-red-700":"bg-orange-600 hover:bg-orange-700")+" text-white",children:k?N.jsxs(N.Fragment,{children:[N.jsx(m,{className:"w-4 h-4 mr-2"}),"Stop Rec"]}):N.jsxs(N.Fragment,{children:[N.jsx(b,{className:"w-4 h-4 mr-2"}),"Start Rec"]})})]})]})]}),N.jsxs("div",{className:"text-center text-purple-300 text-xs",children:[N.jsx("p",{children:"Squirtvana PWA v1.0 - Mobile Streaming Control"}),N.jsx("p",{className:"flex items-center justify-center gap-1 mt-1",children:I.backend?N.jsxs(N.Fragment,{children:[N.jsx(g,{className:"w-3 h-3"}),"Connected"]}):N.jsxs(N.Fragment,{children:[N.jsx(h,{className:"w-3 h-3"}),"Disconnected"]})})]})]})})}Ce.displayName="Textarea",S.createRoot(document.getElementById("root")).render(N.jsx(t.StrictMode,{children:N.jsx(ze,{})}));
